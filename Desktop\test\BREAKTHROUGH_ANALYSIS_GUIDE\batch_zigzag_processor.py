"""
批量ZigZag处理模块
整合股票池筛选、数据获取、ZigZag计算和数据库存储
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from stock_pool import StockPoolSelector, StockPoolConfig, StockInfo
from batch_data_loader import BatchDataLoader, BatchDataConfig
from database_schema import DatabaseManager, get_database
from zigzag import ZigZagState, update_state, BatchZigZagProcessor
from data_models import StockData

logger = logging.getLogger(__name__)

class IntegratedStockAnalyzer:
    """集成股票分析器"""
    
    def __init__(self, 
                 pool_config: StockPoolConfig = None,
                 data_config: BatchDataConfig = None,
                 zigzag_config: Dict = None,
                 db_path: str = "stock_analysis.duckdb"):
        """
        初始化集成分析器
        
        Args:
            pool_config: 股票池筛选配置
            data_config: 批量数据获取配置  
            zigzag_config: ZigZag计算配置
            db_path: 数据库路径
        """
        self.pool_selector = StockPoolSelector(pool_config)
        self.data_loader = BatchDataLoader(data_config)
        self.zigzag_processor = BatchZigZagProcessor(zigzag_config)
        self.db_manager = DatabaseManager(db_path)
        
    def initialize_system(self):
        """初始化系统（连接数据库等）"""
        try:
            self.db_manager.connect()
            logger.info("系统初始化完成")
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            raise
    
    def shutdown_system(self):
        """关闭系统"""
        try:
            self.db_manager.disconnect()
            logger.info("系统已关闭")
        except Exception as e:
            logger.error(f"系统关闭失败: {e}")
    
    def create_and_analyze_stock_pool(self, 
                                    target_count: int = 100,
                                    start_date: str = None,
                                    end_date: str = None,
                                    save_to_db: bool = True) -> Dict[str, Any]:
        """
        创建股票池并进行完整分析
        
        Args:
            target_count: 目标股票数量
            start_date: 历史数据开始日期
            end_date: 历史数据结束日期
            save_to_db: 是否保存到数据库
            
        Returns:
            分析结果字典
        """
        logger.info(f"开始创建和分析股票池 (目标数量: {target_count})")
        
        # 设置默认日期
        if end_date is None:
            end_date = datetime.now().strftime('%Y%m%d')
        if start_date is None:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        
        results = {
            'start_time': datetime.now(),
            'config': {
                'target_count': target_count,
                'start_date': start_date,
                'end_date': end_date
            },
            'stock_pool': [],
            'zigzag_states': {},
            'summary': {},
            'errors': []
        }
        
        try:
            # 1. 筛选股票池
            logger.info("步骤1: 筛选股票池")

            # 获取Tushare配置
            use_tushare = False
            token = None
            if hasattr(self.data_loader, 'config') and self.data_loader.config:
                use_tushare = self.data_loader.config.data_source == 'tushare'
                token = self.data_loader.config.tushare_token

            stock_pool = self.pool_selector.select_stock_pool(target_count, use_tushare, token)
            results['stock_pool'] = stock_pool
            
            if not stock_pool:
                raise ValueError("股票池筛选结果为空")
            
            # 2. 批量获取历史数据
            logger.info("步骤2: 批量获取历史数据")
            stock_data_dict = self.data_loader.get_stock_pool_data(stock_pool, start_date, end_date)
            
            if not stock_data_dict:
                raise ValueError("未能获取任何股票历史数据")
            
            # 3. 从数据库加载现有ZigZag状态
            logger.info("步骤3: 加载现有ZigZag状态")
            existing_states = {}
            if save_to_db:
                for symbol in stock_data_dict.keys():
                    state = self.db_manager.get_zigzag_state(symbol)
                    if state:
                        existing_states[symbol] = state
            
            # 4. 批量计算ZigZag
            logger.info("步骤4: 批量计算ZigZag")
            zigzag_states = self.zigzag_processor.process_stock_pool_batch(
                stock_data_dict, existing_states
            )
            results['zigzag_states'] = zigzag_states
            
            # 5. 保存到数据库
            if save_to_db:
                logger.info("步骤5: 保存到数据库")
                self._save_results_to_database(stock_pool, stock_data_dict, zigzag_states)
            
            # 6. 生成摘要
            results['summary'] = self._generate_analysis_summary(stock_pool, zigzag_states)
            
            results['end_time'] = datetime.now()
            results['duration'] = (results['end_time'] - results['start_time']).total_seconds()
            
            logger.info(f"股票池分析完成，耗时: {results['duration']:.1f}秒")
            
        except Exception as e:
            logger.error(f"股票池分析失败: {e}")
            results['errors'].append(str(e))
            raise
        
        return results
    
    def _save_results_to_database(self, 
                                stock_pool: List[StockInfo],
                                stock_data_dict: Dict[str, List[StockData]],
                                zigzag_states: Dict[str, ZigZagState]):
        """保存结果到数据库"""
        try:
            # 保存股票基本信息
            for stock in stock_pool:
                try:
                    record = {
                        'symbol': stock.symbol,
                        'name': stock.name,
                        'market_cap': stock.market_cap,
                        'industry': stock.industry,
                        'list_date': stock.list_date,
                        'is_active': True
                    }
                    df = pd.DataFrame([record])
                    self.db_manager.conn.execute("INSERT OR REPLACE INTO stock_info SELECT * FROM df")
                except Exception as e:
                    logger.warning(f"保存股票信息失败 {stock.symbol}: {e}")
            
            # 保存历史数据
            self.db_manager.insert_stock_data_batch(stock_data_dict)
            
            # 保存ZigZag状态和拐点
            for symbol, state in zigzag_states.items():
                try:
                    # 保存状态
                    self.db_manager.insert_zigzag_state(state)
                    
                    # 保存拐点
                    if state.confirmed_points:
                        self.db_manager.insert_zigzag_points_batch(symbol, state.confirmed_points)
                        
                except Exception as e:
                    logger.warning(f"保存ZigZag数据失败 {symbol}: {e}")
            
            logger.info("数据库保存完成")
            
        except Exception as e:
            logger.error(f"保存到数据库失败: {e}")
            raise
    
    def _generate_analysis_summary(self, 
                                 stock_pool: List[StockInfo],
                                 zigzag_states: Dict[str, ZigZagState]) -> Dict[str, Any]:
        """生成分析摘要"""
        
        # 股票池摘要
        pool_summary = {
            'total_stocks': len(stock_pool),
            'avg_market_cap': sum(s.market_cap for s in stock_pool) / len(stock_pool) if stock_pool else 0,
            'avg_price': sum(s.price for s in stock_pool) / len(stock_pool) if stock_pool else 0,
            'price_range': {
                'min': min(s.price for s in stock_pool) if stock_pool else 0,
                'max': max(s.price for s in stock_pool) if stock_pool else 0
            }
        }
        
        # ZigZag分析摘要
        zigzag_summary = self.zigzag_processor.get_batch_summary(zigzag_states)
        
        # 趋势分析
        trend_analysis = self._analyze_current_trends(zigzag_states)
        
        return {
            'pool_summary': pool_summary,
            'zigzag_summary': zigzag_summary,
            'trend_analysis': trend_analysis,
            'generated_at': datetime.now().isoformat()
        }
    
    def _analyze_current_trends(self, zigzag_states: Dict[str, ZigZagState]) -> Dict[str, Any]:
        """分析当前趋势分布"""
        trend_stats = {
            'UP': [],
            'DOWN': [],
            'None': []
        }
        
        for symbol, state in zigzag_states.items():
            trend = state.trend or 'None'
            trend_stats[trend].append({
                'symbol': symbol,
                'bars_in_trend': state.bars_in_trend,
                'confirmed_points': len(state.confirmed_points)
            })
        
        # 计算统计信息
        analysis = {}
        for trend, stocks in trend_stats.items():
            if stocks:
                analysis[trend] = {
                    'count': len(stocks),
                    'percentage': len(stocks) / len(zigzag_states) * 100,
                    'avg_bars_in_trend': sum(s['bars_in_trend'] for s in stocks) / len(stocks),
                    'symbols': [s['symbol'] for s in stocks]
                }
            else:
                analysis[trend] = {
                    'count': 0,
                    'percentage': 0,
                    'avg_bars_in_trend': 0,
                    'symbols': []
                }
        
        return analysis
    
    def update_existing_pool(self, symbols: List[str] = None) -> Dict[str, Any]:
        """
        更新现有股票池的数据
        
        Args:
            symbols: 指定要更新的股票代码列表，None表示更新所有
            
        Returns:
            更新结果
        """
        logger.info("开始更新现有股票池数据")
        
        try:
            # 获取要更新的股票列表
            if symbols is None:
                # 从数据库获取所有活跃股票
                df = self.db_manager.conn.execute(
                    "SELECT symbol FROM stock_info WHERE is_active = TRUE"
                ).df()
                symbols = df['symbol'].tolist()
            
            if not symbols:
                logger.warning("没有找到需要更新的股票")
                return {'updated_count': 0, 'errors': []}
            
            # 获取最新数据（最近30天）
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            
            # 批量获取数据
            stock_data_dict = self.data_loader.get_batch_stock_data(symbols, start_date, end_date)
            
            # 转换为StockData对象
            converted_data = self.data_loader.convert_to_stock_data_batch(stock_data_dict)
            
            # 加载现有ZigZag状态
            existing_states = {}
            for symbol in converted_data.keys():
                state = self.db_manager.get_zigzag_state(symbol)
                if state:
                    existing_states[symbol] = state
            
            # 更新ZigZag计算
            updated_states = self.zigzag_processor.process_stock_pool_batch(
                converted_data, existing_states
            )
            
            # 保存更新结果
            self._save_results_to_database([], converted_data, updated_states)
            
            return {
                'updated_count': len(updated_states),
                'symbols': list(updated_states.keys()),
                'summary': self.zigzag_processor.get_batch_summary(updated_states),
                'errors': []
            }
            
        except Exception as e:
            logger.error(f"更新股票池失败: {e}")
            return {'updated_count': 0, 'errors': [str(e)]}

def run_full_analysis(target_count: int = 100,
                     days_history: int = 365,
                     db_path: str = "stock_analysis.duckdb",
                     data_config: 'BatchDataConfig' = None,
                     pool_config: StockPoolConfig = None,
                     zigzag_config: Dict = None) -> Dict[str, Any]:
    """
    运行完整的股票分析流程

    Args:
        target_count: 目标股票数量
        days_history: 历史数据天数
        db_path: 数据库路径
        data_config: 批量数据获取配置
        pool_config: 股票池筛选配置
        zigzag_config: ZigZag计算配置

    Returns:
        分析结果
    """
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 计算日期范围
    end_date = datetime.now().strftime('%Y%m%d')
    start_date = (datetime.now() - timedelta(days=days_history)).strftime('%Y%m%d')

    # 创建分析器
    analyzer = IntegratedStockAnalyzer(
        pool_config=pool_config,
        data_config=data_config,
        zigzag_config=zigzag_config,
        db_path=db_path
    )
    
    try:
        # 初始化系统
        logger.info("正在初始化系统...")
        analyzer.initialize_system()
        logger.info("系统初始化完成")

        # 运行完整分析
        logger.info(f"开始分析: {target_count}只股票, {days_history}天历史数据")
        results = analyzer.create_and_analyze_stock_pool(
            target_count=target_count,
            start_date=start_date,
            end_date=end_date,
            save_to_db=True
        )

        # 打印摘要
        print_analysis_summary(results)

        return results

    except Exception as e:
        logger.error(f"完整分析过程失败: {e}")
        raise
    finally:
        try:
            analyzer.shutdown_system()
        except:
            pass

def print_analysis_summary(results: Dict[str, Any]):
    """打印分析结果摘要"""
    print("\n" + "="*60)
    print("股票池ZigZag分析结果摘要")
    print("="*60)
    
    # 基本信息
    config = results.get('config', {})
    print(f"分析期间: {config.get('start_date')} - {config.get('end_date')}")
    print(f"目标股票数量: {config.get('target_count')}")
    print(f"实际处理时间: {results.get('duration', 0):.1f}秒")
    
    # 股票池信息
    pool_summary = results.get('summary', {}).get('pool_summary', {})
    print(f"\n股票池信息:")
    print(f"  总股票数: {pool_summary.get('total_stocks', 0)}")
    print(f"  平均市值: {pool_summary.get('avg_market_cap', 0):.1f}亿元")
    print(f"  平均价格: {pool_summary.get('avg_price', 0):.2f}元")
    
    price_range = pool_summary.get('price_range', {})
    print(f"  价格范围: {price_range.get('min', 0):.2f} - {price_range.get('max', 0):.2f}元")
    
    # ZigZag分析信息
    zigzag_summary = results.get('summary', {}).get('zigzag_summary', {})
    print(f"\nZigZag分析信息:")
    print(f"  有拐点的股票: {zigzag_summary.get('stocks_with_points', 0)}")
    print(f"  总拐点数: {zigzag_summary.get('total_confirmed_points', 0)}")
    print(f"  平均每股拐点数: {zigzag_summary.get('avg_points_per_stock', 0):.1f}")
    
    # 趋势分析
    trend_analysis = results.get('summary', {}).get('trend_analysis', {})
    print(f"\n当前趋势分布:")
    for trend, info in trend_analysis.items():
        count = info.get('count', 0)
        percentage = info.get('percentage', 0)
        print(f"  {trend}: {count}只 ({percentage:.1f}%)")
    
    # 错误信息
    errors = results.get('errors', [])
    if errors:
        print(f"\n错误信息:")
        for error in errors:
            print(f"  - {error}")
    
    print("="*60)

if __name__ == "__main__":
    # 示例使用
    print("开始运行完整股票分析...")
    
    try:
        # 运行分析（50只股票，1年历史数据）
        results = run_full_analysis(
            target_count=50,
            days_history=365,
            db_path="demo_stock_analysis.duckdb"
        )
        
        print("\n分析完成！")
        
        # 可以进一步处理结果
        zigzag_states = results.get('zigzag_states', {})
        
        # 显示一些有趣的股票
        print("\n一些有趣的股票:")
        for symbol, state in list(zigzag_states.items())[:5]:
            points_count = len(state.confirmed_points)
            trend = state.trend or "无趋势"
            print(f"  {symbol}: {points_count}个拐点, 当前趋势: {trend}")
        
    except Exception as e:
        print(f"分析失败: {e}")
        logger.error(f"完整分析失败: {e}")
