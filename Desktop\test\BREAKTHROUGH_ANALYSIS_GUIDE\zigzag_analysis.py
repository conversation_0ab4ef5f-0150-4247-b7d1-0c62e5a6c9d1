import akshare as ak
import pandas as pd
from datetime import datetime
from dataclasses import dataclass
import json
import os
from decimal import Decimal, getcontext
from typing import List, Tuple, Dict, Any, Optional

# 设置Decimal精度
getcontext().prec = 28

@dataclass
class StockData:
    """股票数据模型"""
    symbol: str
    date: datetime
    open: Decimal
    high: Decimal
    low: Decimal
    close: Decimal
    volume: int
    
    # 技术指标字段
    vol5: Optional[Decimal] = None
    vol20: Optional[Decimal] = None

    # 新增的 ZigZag 和趋势字段
    is_zigzag_point: bool = False
    zigzag_point_type: Optional[str] = None  # 'HIGH' or 'LOW'
    trend_status: Optional[str] = None

@dataclass
class ZigZagPoint:
    """ZigZag转折点数据类"""
    index: int
    price: Decimal
    point_type: str  # 'HIGH' or 'LOW'
    date: str
    threshold_used: Decimal  # 记录使用的阈值
    

def get_stock_data(stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    从 AKShare 获取股票历史数据
    :param stock_code: 股票代码 (如 "600000")
    :param start_date: 开始日期 (格式 "YYYYMMDD")
    :param end_date: 结束日期 (格式 "YYYYMMDD")
    :return: 包含 OHLCV 数据的 DataFrame
    """
    try:
        # 获取后复权数据（包含分红送股）
        df = ak.stock_zh_a_hist(
            symbol=stock_code, 
            period="daily", 
            start_date=start_date, 
            end_date=end_date, 
            # adjust="qfq"
        )
        
        # 规范列名
        df = df.rename(columns={
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume'
        })
        
        # 转换日期格式
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)
        
        # 计算成交量均线（5日与20日移动平均）
        df['vol5'] = df['volume'].rolling(window=5, min_periods=1).mean()
        df['vol20'] = df['volume'].rolling(window=20, min_periods=1).mean()

        return df[['date', 'open', 'high', 'low', 'close', 'volume', 'vol5', 'vol20']]
    
    except Exception as e:
        raise RuntimeError(f"获取 {stock_code} 数据失败: {str(e)}")

def dataframe_to_stock_data(df: pd.DataFrame, symbol: str) -> List[StockData]:
    """使用列表推导式优化，将 DataFrame 转换为 StockData 列表"""
    return [
        StockData(
            symbol=symbol,
            date=row.date.to_pydatetime(),
            open=Decimal(str(row.open)),
            high=Decimal(str(row.high)),
            low=Decimal(str(row.low)),
            close=Decimal(str(row.close)),
            volume=int(row.volume)
        )
        for row in df.itertuples()
    ]

class ZigZagCalculator:
    """基于ATR动态幅度的ZigZag指标计算器"""
    
    def __init__(self, atr_period: int = 14, atr_multiplier: float = 2.0):
        """
        初始化ZigZag计算器
        
        Args:
            atr_period: ATR计算周期
            atr_multiplier: ATR倍数，用于确定ZigZag的最小幅度
        """
        self.atr_period = atr_period
        self.atr_multiplier = atr_multiplier
    
    def calculate_atr(self, stock_data: List[StockData]) -> List[float]:
        """
        计算ATR (Average True Range)
        
        Args:
            stock_data: 股票数据列表
            
        Returns:
            ATR值列表
        """
        if len(stock_data) < 2:
            return [0.0] * len(stock_data)
        
        true_ranges = []
        
        for i in range(len(stock_data)):
            if i == 0:
                # 第一个数据点，TR = High - Low
                tr = float(stock_data[i].high - stock_data[i].low)
            else:
                # TR = max(High-Low, |High-PrevClose|, |Low-PrevClose|)
                hl = float(stock_data[i].high - stock_data[i].low)
                hc = abs(float(stock_data[i].high - stock_data[i-1].close))
                lc = abs(float(stock_data[i].low - stock_data[i-1].close))
                tr = max(hl, hc, lc)
            
            true_ranges.append(tr)
        
        # 计算ATR (简单移动平均)
        atr_values = []
        for i in range(len(true_ranges)):
            if i < self.atr_period - 1:
                # 不足周期时，使用已有数据的平均值
                atr = sum(true_ranges[:i+1]) / (i+1)
            else:
                # 使用指定周期的移动平均
                atr = sum(true_ranges[i-self.atr_period+1:i+1]) / self.atr_period
            
            atr_values.append(atr)
        
        return atr_values
    
    def calculate_zigzag(self, stock_data: List[StockData]) -> List[StockData]:
        """
        计算ZigZag指标并更新股票数据
        
        Args:
            stock_data: 原始股票数据列表
            
        Returns:
            更新了ZigZag信息的股票数据列表
        """
        if len(stock_data) < 3:
            return stock_data
        
        # 创建数据副本避免修改原数据
        data = [StockData(
            symbol=item.symbol,
            date=item.date,
            open=item.open,
            high=item.high,
            low=item.low,
            close=item.close,
            volume=item.volume,
            vol5=item.vol5,
            vol20=item.vol20
        ) for item in stock_data]
        
        # 计算ATR
        atr_values = self.calculate_atr(data)
        
        # 初始化ZigZag计算变量
        zigzag_points = []
        current_trend = None  # 'UP' or 'DOWN'
        last_extreme_idx = 0
        last_extreme_price = float(data[0].high)
        
        # 从第二个数据点开始计算
        for i in range(1, len(data)):
            current_high = float(data[i].high)
            current_low = float(data[i].low)
            current_atr = atr_values[i]
            threshold = current_atr * self.atr_multiplier
            
            # 检查是否形成新的高点
            if current_high > last_extreme_price + threshold:
                if current_trend != 'UP':
                    # 趋势转为上升，标记前一个低点
                    if current_trend == 'DOWN' and last_extreme_idx > 0:
                        zigzag_points.append((last_extreme_idx, 'LOW'))
                    
                    current_trend = 'UP'
                    last_extreme_idx = i
                    last_extreme_price = current_high
                elif current_high > last_extreme_price:
                    # 更新高点
                    last_extreme_idx = i
                    last_extreme_price = current_high
            
            # 检查是否形成新的低点
            elif current_low < last_extreme_price - threshold:
                if current_trend != 'DOWN':
                    # 趋势转为下降，标记前一个高点
                    if current_trend == 'UP' and last_extreme_idx > 0:
                        zigzag_points.append((last_extreme_idx, 'HIGH'))
                    
                    current_trend = 'DOWN'
                    last_extreme_idx = i
                    last_extreme_price = current_low
                elif current_low < last_extreme_price:
                    # 更新低点
                    last_extreme_idx = i
                    last_extreme_price = current_low
        
        # 标记最后一个极值点
        if last_extreme_idx > 0:
            point_type = 'HIGH' if current_trend == 'UP' else 'LOW'
            zigzag_points.append((last_extreme_idx, point_type))
        
        # 更新数据中的ZigZag信息
        for idx, point_type in zigzag_points:
            if 0 <= idx < len(data):
                data[idx].is_zigzag_point = True
                data[idx].zigzag_point_type = point_type
        
        # 计算趋势状态
        self._calculate_trend_status(data, zigzag_points)
        
        return data
    
    def _calculate_trend_status(self, data: List[StockData], zigzag_points: List[Tuple[int, str]]):
        """
        根据ZigZag点计算趋势状态
        
        Args:
            data: 股票数据列表
            zigzag_points: ZigZag点列表 [(索引, 类型)]
        """
        if len(zigzag_points) < 2:
            return
        
        current_trend = None
        
        # 遍历所有数据点
        for i in range(len(data)):
            # 找到当前点之前最近的两个ZigZag点
            prev_points = [(idx, ptype) for idx, ptype in zigzag_points if idx <= i]
            
            if len(prev_points) >= 2:
                # 获取最近的两个ZigZag点
                second_last = prev_points[-2]
                last = prev_points[-1]
                
                # 根据最近两个ZigZag点确定趋势
                if second_last[1] == 'LOW' and last[1] == 'HIGH':
                    current_trend = 'UPTREND'
                elif second_last[1] == 'HIGH' and last[1] == 'LOW':
                    current_trend = 'DOWNTREND'
            elif len(prev_points) == 1:
                # 只有一个ZigZag点时，根据类型判断
                if prev_points[0][1] == 'LOW':
                    current_trend = 'UPTREND'
                else:
                    current_trend = 'DOWNTREND'
            
            data[i].trend_status = current_trend
    
    def get_zigzag_summary(self, stock_data: List[StockData]) -> dict:
        """
        获取ZigZag计算结果摘要
        
        Args:
            stock_data: 计算后的股票数据
            
        Returns:
            包含统计信息的字典
        """
        zigzag_points = [item for item in stock_data if item.is_zigzag_point]
        high_points = [item for item in zigzag_points if item.zigzag_point_type == 'HIGH']
        low_points = [item for item in zigzag_points if item.zigzag_point_type == 'LOW']
        
        summary = {
            'total_points': len(stock_data),
            'zigzag_points': len(zigzag_points),
            'high_points': len(high_points),
            'low_points': len(low_points),
            'zigzag_ratio': len(zigzag_points) / len(stock_data) if stock_data else 0,
            'latest_trend': stock_data[-1].trend_status if stock_data else None
        }
        
        if high_points:
            summary['highest_point'] = {
                'date': high_points[0].date,
                'price': float(high_points[0].high),
                'symbol': high_points[0].symbol
            }
            for point in high_points:
                if float(point.high) > summary['highest_point']['price']:
                    summary['highest_point'] = {
                        'date': point.date,
                        'price': float(point.high),
                        'symbol': point.symbol
                    }
        
        if low_points:
            summary['lowest_point'] = {
                'date': low_points[0].date,
                'price': float(low_points[0].low),
                'symbol': low_points[0].symbol
            }
            for point in low_points:
                if float(point.low) < summary['lowest_point']['price']:
                    summary['lowest_point'] = {
                        'date': point.date,
                        'price': float(point.low),
                        'symbol': point.symbol
                    }
        
        return summary