"""
股票分析系统演示脚本
展示主要功能的使用方法
"""

import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demo_stock_pool_selection():
    """演示股票池筛选功能"""
    print("\n" + "="*60)
    print("演示1: 股票池筛选")
    print("="*60)
    
    try:
        from stock_pool import StockPoolSelector, StockPoolConfig
        
        # 创建筛选配置
        config = StockPoolConfig(
            min_market_cap=50.0,   # 最小市值50亿
            max_market_cap=2000.0, # 最大市值2000亿
            min_price=5.0,         # 最小价格5元
            max_price=100.0,       # 最大价格100元
            min_pe_ratio=5.0,      # 最小PE 5倍
            max_pe_ratio=30.0,     # 最大PE 30倍
        )
        
        print("筛选配置:")
        print(f"  市值范围: {config.min_market_cap}-{config.max_market_cap}亿元")
        print(f"  价格范围: {config.min_price}-{config.max_price}元")
        print(f"  PE范围: {config.min_pe_ratio}-{config.max_pe_ratio}倍")
        
        # 创建筛选器并筛选股票池
        selector = StockPoolSelector(config)
        stock_pool = selector.select_stock_pool(20)  # 筛选20只股票进行演示
        
        print(f"\n筛选结果: 共选出 {len(stock_pool)} 只股票")
        
        # 显示前10只股票
        print("\n前10只股票:")
        for i, stock in enumerate(stock_pool[:10]):
            print(f"  {i+1:2d}. {stock.symbol} {stock.name} "
                  f"市值:{stock.market_cap:.0f}亿 价格:{stock.price:.2f}")
        
        return stock_pool
        
    except Exception as e:
        print(f"股票池筛选演示失败: {e}")
        return []

def demo_batch_data_loading(stock_pool):
    """演示批量数据获取功能"""
    print("\n" + "="*60)
    print("演示2: 批量数据获取")
    print("="*60)
    
    if not stock_pool:
        print("没有股票池数据，跳过演示")
        return {}
    
    try:
        from batch_data_loader import BatchDataLoader, BatchDataConfig
        
        # 选择前5只股票进行演示
        demo_symbols = [stock.symbol for stock in stock_pool[:5]]
        
        # 设置日期范围（最近3个月）
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=90)).strftime('%Y%m%d')
        
        print(f"获取数据:")
        print(f"  股票代码: {demo_symbols}")
        print(f"  日期范围: {start_date} - {end_date}")
        
        # 创建数据加载器
        config = BatchDataConfig(
            max_workers=3,      # 3个并发线程
            request_delay=0.2,  # 请求间隔0.2秒
            cache_enabled=True  # 启用缓存
        )
        loader = BatchDataLoader(config)
        
        # 批量获取数据
        data_dict = loader.get_batch_stock_data(demo_symbols, start_date, end_date)
        
        print(f"\n获取结果:")
        for symbol, df in data_dict.items():
            print(f"  {symbol}: {len(df)} 条记录")
        
        # 转换为StockData对象
        stock_data_dict = loader.convert_to_stock_data_batch(data_dict)
        
        return stock_data_dict
        
    except Exception as e:
        print(f"批量数据获取演示失败: {e}")
        return {}

def demo_zigzag_analysis(stock_data_dict):
    """演示ZigZag分析功能"""
    print("\n" + "="*60)
    print("演示3: ZigZag分析")
    print("="*60)
    
    if not stock_data_dict:
        print("没有股票数据，跳过演示")
        return {}
    
    try:
        from zigzag import BatchZigZagProcessor
        
        # 创建ZigZag处理器
        zigzag_config = {
            'atr_period': 14,
            'atr_multiplier': 1.5,
            'min_price_move': 0,
            'min_trend_bars': 1
        }
        processor = BatchZigZagProcessor(zigzag_config)
        
        print("ZigZag配置:")
        print(f"  ATR周期: {zigzag_config['atr_period']}")
        print(f"  ATR倍数: {zigzag_config['atr_multiplier']}")
        
        # 批量处理ZigZag计算
        zigzag_states = processor.process_stock_pool_batch(stock_data_dict)
        
        print(f"\n分析结果:")
        for symbol, state in zigzag_states.items():
            points_count = len(state.confirmed_points)
            trend = state.trend or "无趋势"
            print(f"  {symbol}: {points_count}个拐点, 当前趋势: {trend}")
        
        # 显示摘要
        summary = processor.get_batch_summary(zigzag_states)
        print(f"\n整体摘要:")
        print(f"  总股票数: {summary['total_stocks']}")
        print(f"  有拐点的股票: {summary['stocks_with_points']}")
        print(f"  总拐点数: {summary['total_confirmed_points']}")
        print(f"  平均每股拐点数: {summary['avg_points_per_stock']:.1f}")
        
        # 趋势分布
        print(f"  趋势分布:")
        for trend, count in summary['trend_distribution'].items():
            print(f"    {trend}: {count}只")
        
        return zigzag_states
        
    except Exception as e:
        print(f"ZigZag分析演示失败: {e}")
        return {}

def demo_database_operations(stock_data_dict, zigzag_states):
    """演示数据库操作功能"""
    print("\n" + "="*60)
    print("演示4: 数据库操作")
    print("="*60)
    
    try:
        from database_schema import DatabaseManager
        
        # 创建数据库管理器
        db_manager = DatabaseManager("demo_stock.duckdb")
        db_manager.connect()
        
        print("数据库连接成功")
        
        # 保存股票数据
        if stock_data_dict:
            db_manager.insert_stock_data_batch(stock_data_dict)
            print(f"保存了 {len(stock_data_dict)} 只股票的历史数据")
        
        # 保存ZigZag状态
        if zigzag_states:
            for symbol, state in zigzag_states.items():
                db_manager.insert_zigzag_state(state)
                if state.confirmed_points:
                    db_manager.insert_zigzag_points_batch(symbol, state.confirmed_points)
            print(f"保存了 {len(zigzag_states)} 只股票的ZigZag分析结果")
        
        # 显示数据库统计
        stats = db_manager.get_database_stats()
        print(f"\n数据库统计:")
        print(f"  历史数据记录数: {stats.get('daily_data_count', 0)}")
        print(f"  ZigZag拐点数: {stats.get('zigzag_points_count', 0)}")
        print(f"  策略信号数: {stats.get('strategy_signals_count', 0)}")
        
        db_manager.disconnect()
        print("数据库操作完成")
        
    except Exception as e:
        print(f"数据库操作演示失败: {e}")

def demo_strategy_interface(stock_data_dict, zigzag_states):
    """演示策略接口功能"""
    print("\n" + "="*60)
    print("演示5: 策略接口")
    print("="*60)
    
    if not stock_data_dict or not zigzag_states:
        print("没有足够的数据，跳过演示")
        return
    
    try:
        from strategy_interface import setup_strategy_manager
        
        # 设置策略管理器
        strategy_manager = setup_strategy_manager()
        
        print("已注册的策略:")
        for name, strategy in strategy_manager.strategies.items():
            info = strategy.get_strategy_info()
            print(f"  - {name}: {info['description']}")
        
        # 运行策略分析
        print(f"\n运行策略分析:")
        total_signals = 0
        
        for symbol in list(stock_data_dict.keys())[:3]:  # 只分析前3只股票
            if symbol in zigzag_states:
                stock_data = stock_data_dict[symbol]
                zigzag_state = zigzag_states[symbol]
                
                for strategy_name in strategy_manager.strategies.keys():
                    signals = strategy_manager.run_strategy(
                        strategy_name, symbol, stock_data, zigzag_state, save_signals=False
                    )
                    
                    if signals:
                        print(f"  {symbol} - {strategy_name}: {len(signals)} 个信号")
                        for signal in signals[:2]:  # 只显示前2个信号
                            print(f"    {signal.date.strftime('%Y-%m-%d')} {signal.signal_type} "
                                  f"强度:{signal.strength:.1f} 原因:{signal.reason}")
                        total_signals += len(signals)
        
        print(f"\n总共生成 {total_signals} 个策略信号")
        
    except Exception as e:
        print(f"策略接口演示失败: {e}")

def main():
    """主演示函数"""
    print("股票分析系统功能演示")
    print("本演示将展示系统的主要功能模块")
    
    try:
        # 1. 股票池筛选
        stock_pool = demo_stock_pool_selection()
        
        # 2. 批量数据获取
        stock_data_dict = demo_batch_data_loading(stock_pool)
        
        # 3. ZigZag分析
        zigzag_states = demo_zigzag_analysis(stock_data_dict)
        
        # 4. 数据库操作
        demo_database_operations(stock_data_dict, zigzag_states)
        
        # 5. 策略接口
        demo_strategy_interface(stock_data_dict, zigzag_states)
        
        print("\n" + "="*60)
        print("演示完成！")
        print("="*60)
        print("系统功能演示已全部完成。")
        print("您可以查看生成的 demo_stock.duckdb 数据库文件。")
        print("要运行完整分析，请使用: python main.py --mode full")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        logger.error(f"演示失败: {e}")

if __name__ == "__main__":
    main()
