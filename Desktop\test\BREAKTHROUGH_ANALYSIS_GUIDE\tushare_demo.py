"""
Tushare数据源演示脚本
展示使用Tushare获取股票数据的功能
"""

import logging
from datetime import datetime, timedelta
from typing import List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demo_tushare_config():
    """演示Tushare配置"""
    print("\n" + "="*60)
    print("演示1: Tushare配置")
    print("="*60)
    
    try:
        from tushare_config import get_tushare_config, setup_tushare_token
        
        config = get_tushare_config()
        
        if config.is_configured():
            print(f"✅ Tushare已配置，Token: {config.get_token()[:10]}...")
            return True
        else:
            print("❌ Tushare未配置")
            print("请运行以下命令配置:")
            print("  python main.py --mode config")
            print("或者设置环境变量:")
            print("  set TUSHARE_TOKEN=你的token")
            return False
            
    except Exception as e:
        print(f"配置检查失败: {e}")
        return False

def demo_basic_data_loading():
    """演示基础数据获取"""
    print("\n" + "="*60)
    print("演示2: 基础数据获取")
    print("="*60)
    
    try:
        from data_loader import get_stock_data, get_stock_basic_info
        from tushare_config import get_tushare_config
        
        config = get_tushare_config()
        if not config.is_configured():
            print("跳过演示 - Tushare未配置")
            return
        
        # 测试获取股票基本信息
        print("获取股票基本信息...")
        basic_info = get_stock_basic_info(config.get_token())
        print(f"✅ 获取到 {len(basic_info)} 只股票基本信息")
        
        # 显示前5只股票
        print("前5只股票:")
        for i, row in basic_info.head().iterrows():
            print(f"  {row['ts_code']} {row['name']} {row['industry']}")
        
        # 测试获取历史数据
        print(f"\n获取平安银行历史数据...")
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        df = get_stock_data('000001', start_date, end_date, config.get_token())
        
        if not df.empty:
            print(f"✅ 获取到 {len(df)} 条历史数据")
            print("最近5天数据:")
            for i, row in df.tail().iterrows():
                print(f"  {row['date'].strftime('%Y-%m-%d')} "
                      f"开:{row['open']:.2f} "
                      f"高:{row['high']:.2f} "
                      f"低:{row['low']:.2f} "
                      f"收:{row['close']:.2f}")
        else:
            print("❌ 未获取到历史数据")
            
    except Exception as e:
        print(f"数据获取演示失败: {e}")
        logger.error(f"数据获取失败: {e}")

def demo_batch_data_loading():
    """演示批量数据获取"""
    print("\n" + "="*60)
    print("演示3: 批量数据获取")
    print("="*60)
    
    try:
        from batch_data_loader import BatchDataLoader, BatchDataConfig
        from tushare_config import get_tushare_config
        
        config = get_tushare_config()
        if not config.is_configured():
            print("跳过演示 - Tushare未配置")
            return
        
        # 创建批量数据加载器
        data_config = BatchDataConfig(
            tushare_token=config.get_token(),
            data_source='tushare',
            max_workers=2,  # 减少并发数
            request_delay=0.3,  # 增加请求间隔
            cache_enabled=True
        )
        
        loader = BatchDataLoader(data_config)
        
        # 测试股票列表
        test_symbols = ['000001', '000002', '600000', '600036', '000858']
        
        print(f"批量获取 {len(test_symbols)} 只股票的30天历史数据...")
        
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        # 批量获取数据
        data_dict = loader.get_batch_stock_data(test_symbols, start_date, end_date)
        
        print(f"✅ 成功获取 {len(data_dict)} 只股票数据:")
        for symbol, df in data_dict.items():
            print(f"  {symbol}: {len(df)} 条记录")
        
        # 转换为StockData对象
        stock_data_dict = loader.convert_to_stock_data_batch(data_dict)
        print(f"✅ 成功转换 {len(stock_data_dict)} 只股票数据")
        
        return stock_data_dict
        
    except Exception as e:
        print(f"批量数据获取演示失败: {e}")
        logger.error(f"批量获取失败: {e}")
        return {}

def demo_zigzag_with_tushare_data(stock_data_dict):
    """演示使用Tushare数据进行ZigZag分析"""
    print("\n" + "="*60)
    print("演示4: ZigZag分析 (Tushare数据)")
    print("="*60)
    
    if not stock_data_dict:
        print("跳过演示 - 没有股票数据")
        return
    
    try:
        from zigzag import BatchZigZagProcessor
        
        # 创建ZigZag处理器
        zigzag_config = {
            'atr_period': 14,
            'atr_multiplier': 1.5,
            'min_price_move': 0,
            'min_trend_bars': 1
        }
        
        processor = BatchZigZagProcessor(zigzag_config)
        
        print(f"对 {len(stock_data_dict)} 只股票进行ZigZag分析...")
        
        # 批量处理
        zigzag_states = processor.process_stock_pool_batch(stock_data_dict)
        
        print(f"✅ ZigZag分析完成:")
        for symbol, state in zigzag_states.items():
            points_count = len(state.confirmed_points)
            trend = state.trend or "无趋势"
            print(f"  {symbol}: {points_count}个拐点, 趋势: {trend}")
            
            # 显示最近的拐点
            if state.confirmed_points:
                recent_point = state.confirmed_points[-1]
                print(f"    最近拐点: {recent_point.date} {recent_point.point_type} {recent_point.price}")
        
        # 显示整体摘要
        summary = processor.get_batch_summary(zigzag_states)
        print(f"\n整体摘要:")
        print(f"  总拐点数: {summary['total_confirmed_points']}")
        print(f"  平均每股拐点: {summary['avg_points_per_stock']:.1f}")
        print(f"  趋势分布: {summary['trend_distribution']}")
        
        return zigzag_states
        
    except Exception as e:
        print(f"ZigZag分析演示失败: {e}")
        logger.error(f"ZigZag分析失败: {e}")
        return {}

def demo_performance_comparison():
    """演示性能对比"""
    print("\n" + "="*60)
    print("演示5: 性能对比 (Tushare vs AKShare)")
    print("="*60)
    
    try:
        from tushare_config import get_tushare_config
        import time
        
        config = get_tushare_config()
        if not config.is_configured():
            print("跳过演示 - Tushare未配置")
            return
        
        test_symbol = '000001'
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        # 测试Tushare
        print("测试Tushare数据获取速度...")
        start_time = time.time()
        
        try:
            from data_loader import get_stock_data
            df_tushare = get_stock_data(test_symbol, start_date, end_date, config.get_token())
            tushare_time = time.time() - start_time
            tushare_records = len(df_tushare) if not df_tushare.empty else 0
            print(f"✅ Tushare: {tushare_records} 条记录, 耗时: {tushare_time:.2f}秒")
        except Exception as e:
            print(f"❌ Tushare测试失败: {e}")
            tushare_time = None
            tushare_records = 0
        
        # 测试AKShare
        print("测试AKShare数据获取速度...")
        start_time = time.time()
        
        try:
            import akshare as ak
            df_akshare = ak.stock_zh_a_hist(
                symbol=test_symbol,
                period="daily",
                start_date=start_date,
                end_date=end_date,
                adjust="qfq"
            )
            akshare_time = time.time() - start_time
            akshare_records = len(df_akshare) if not df_akshare.empty else 0
            print(f"✅ AKShare: {akshare_records} 条记录, 耗时: {akshare_time:.2f}秒")
        except Exception as e:
            print(f"❌ AKShare测试失败: {e}")
            akshare_time = None
            akshare_records = 0
        
        # 对比结果
        print(f"\n性能对比结果:")
        if tushare_time and akshare_time:
            if tushare_time < akshare_time:
                print(f"🏆 Tushare更快 ({tushare_time:.2f}s vs {akshare_time:.2f}s)")
            else:
                print(f"🏆 AKShare更快 ({akshare_time:.2f}s vs {tushare_time:.2f}s)")
        
        print(f"数据完整性:")
        print(f"  Tushare: {tushare_records} 条记录")
        print(f"  AKShare: {akshare_records} 条记录")
        
    except Exception as e:
        print(f"性能对比演示失败: {e}")
        logger.error(f"性能对比失败: {e}")

def main():
    """主演示函数"""
    print("Tushare数据源功能演示")
    print("本演示展示使用Tushare获取股票数据的功能")
    
    try:
        # 1. 检查Tushare配置
        is_configured = demo_tushare_config()
        
        if not is_configured:
            print("\n请先配置Tushare token:")
            print("1. 访问 https://tushare.pro/ 注册获取免费token")
            print("2. 运行: python main.py --mode config")
            print("3. 或设置环境变量: set TUSHARE_TOKEN=你的token")
            return
        
        # 2. 基础数据获取演示
        demo_basic_data_loading()
        
        # 3. 批量数据获取演示
        stock_data_dict = demo_batch_data_loading()
        
        # 4. ZigZag分析演示
        zigzag_states = demo_zigzag_with_tushare_data(stock_data_dict)
        
        # 5. 性能对比演示
        demo_performance_comparison()
        
        print("\n" + "="*60)
        print("Tushare演示完成！")
        print("="*60)
        print("✅ Tushare数据源集成成功")
        print("✅ 支持股票基本信息获取")
        print("✅ 支持历史数据批量获取")
        print("✅ 支持ZigZag技术分析")
        print("✅ 提供性能优化配置")
        
        print(f"\n系统优势:")
        print("🚀 更稳定的数据源")
        print("📊 更丰富的数据字段")
        print("⚡ 可配置的请求频率")
        print("💾 智能缓存机制")
        print("🔄 自动重试机制")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        logger.error(f"演示失败: {e}")

if __name__ == "__main__":
    main()
