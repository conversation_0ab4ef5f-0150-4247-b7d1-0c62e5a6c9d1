import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd
import json

from batch_data_loader import BatchDataLoader, BatchDataConfig, StockData
from tushare_config import get_tushare_config
from zigzag_analysis import ZigZagCalculator


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def _convert_to_stock_data(df: pd.DataFrame, symbol: str) -> List[StockData]:
    """将DataFrame转换为StockData列表"""
    stock_data_list = []
    
    for _, row in df.iterrows():
        try:
            if 'date' in df.columns:
                date = pd.to_datetime(row['date'])
                open_price = float(row['open'])
                high_price = float(row['high'])
                low_price = float(row['low'])
                close_price = float(row['close'])
                volume = int(row['volume'])
            else:
                continue
            
            stock_data = StockData(
                symbol=symbol,
                date=date,
                open=open_price,
                high=high_price,
                low=low_price,
                close=close_price,
                volume=volume
            )
            stock_data_list.append(stock_data)
            
        except Exception as e:
            logger.warning(f"转换数据行失败: {e}")
            continue
    
    return sorted(stock_data_list, key=lambda x: x.date)

stock_symbols = ['003021']
years = 5

tushare_config = get_tushare_config()
batch_data_loader = BatchDataLoader(tushare_config)
data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.5
    )

end_date = datetime.now().strftime('%Y%m%d')
start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y%m%d')
print(f"开始专业分析 {stock_symbols[0]}等{len(stock_symbols)} 只股票的 {years} 年数据...")
print(f"时间范围: {start_date} 到 {end_date}")

stock_data_dict = batch_data_loader.get_batch_stock_data(stock_symbols, start_date, end_date)

# ZigZag分析
zigzag_calculator = ZigZagCalculator(atr_period=14, atr_multiplier=2.0)

# 分析每只股票
for symbol in stock_symbols:
    try:
        df = stock_data_dict.get(symbol)
        if df is None or df.empty:
            logger.warning(f"股票 {symbol} 数据为空，跳过分析")
            continue
        
        # 转换为StockData格式
        stock_data_list = _convert_to_stock_data(df, symbol)
        
        # 专业突破分析
        breakthrough_analysis = self._professional_breakthrough_analysis(
            stock_data_list, zigzag_points
        )
        
        results['detailed_results'][symbol] = breakthrough_analysis
        results['successful_analysis'] += 1
        
        # 如果发现突破回调模式，加入结果列表
        if breakthrough_analysis['has_breakthrough_pullback']:
            results['stocks_with_breakthrough'].append({
                'symbol': symbol,
                'breakthrough_strength': breakthrough_analysis['breakthrough_strength'],
                'pullback_depth': breakthrough_analysis['pullback_depth'],
                'current_position': breakthrough_analysis['current_position']
            })
        
        print(f"✅ {symbol} 专业分析完成")
        
    except Exception as e:
        logger.error(f"分析股票 {symbol} 失败: {e}")
        continue
