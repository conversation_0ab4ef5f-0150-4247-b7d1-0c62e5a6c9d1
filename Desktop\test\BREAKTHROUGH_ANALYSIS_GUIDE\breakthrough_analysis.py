"""
突破回调分析模块
分析股票的ZigZag峰谷数据，识别突破和回调模式
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd

from batch_data_loader import BatchDataLoader, BatchDataConfig
from zigzag_analysis import ZigZagCalculator
from zigzag import ZigZagState
from data_models import StockData
from tushare_config import get_tushare_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BreakthroughAnalyzer:
    """突破回调分析器"""
    
    def __init__(self, data_config: BatchDataConfig):
        self.data_config = data_config
        self.data_loader = BatchDataLoader(data_config)
        # 使用float类型避免Decimal兼容性问题
        self.zigzag_calculator = ZigZagCalculator(atr_period=14, atr_multiplier=2.0)
    
    def analyze_stocks(self, stock_symbols: List[str], years: int = 5) -> Dict:
        """
        分析股票列表的突破回调模式
        
        Args:
            stock_symbols: 股票代码列表
            years: 分析年数
            
        Returns:
            分析结果字典
        """
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y%m%d')
        
        print(f"开始分析 {len(stock_symbols)} 只股票的 {years} 年数据...")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 批量获取数据
        stock_data_dict = self.data_loader.get_batch_stock_data(
            stock_symbols, start_date, end_date
        )
        
        results = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'time_range': f"{start_date} 到 {end_date}",
            'total_stocks': len(stock_symbols),
            'successful_analysis': 0,
            'stocks_with_breakthrough': [],
            'detailed_results': {}
        }
        
        # 分析每只股票
        for symbol in stock_symbols:
            try:
                df = stock_data_dict.get(symbol)
                if df is None or df.empty:
                    logger.warning(f"股票 {symbol} 数据为空，跳过分析")
                    continue
                
                # 转换为StockData格式
                stock_data_list = self._convert_to_stock_data(df, symbol)
                
                # 进行ZigZag分析
                zigzag_state = self._analyze_zigzag(stock_data_list)
                
                # 分析突破回调模式
                breakthrough_analysis = self._analyze_breakthrough_pullback(
                    stock_data_list, zigzag_state
                )
                
                results['detailed_results'][symbol] = breakthrough_analysis
                results['successful_analysis'] += 1
                
                # 如果发现突破回调模式，加入结果列表
                if breakthrough_analysis['has_breakthrough_pullback']:
                    results['stocks_with_breakthrough'].append({
                        'symbol': symbol,
                        'breakthrough_strength': breakthrough_analysis['breakthrough_strength'],
                        'pullback_depth': breakthrough_analysis['pullback_depth'],
                        'current_position': breakthrough_analysis['current_position']
                    })
                
                print(f"✅ {symbol} 分析完成")
                
            except Exception as e:
                logger.error(f"分析股票 {symbol} 失败: {e}")
                continue
        
        # 按突破强度排序
        results['stocks_with_breakthrough'].sort(
            key=lambda x: x['breakthrough_strength'], reverse=True
        )
        
        return results
    
    def _convert_to_stock_data(self, df: pd.DataFrame, symbol: str) -> List[StockData]:
        """将DataFrame转换为StockData列表"""
        stock_data_list = []

        # 打印列名以便调试
        logger.info(f"数据列名: {list(df.columns)}")

        for _, row in df.iterrows():
            try:
                # 处理不同数据源的列名
                if 'trade_date' in df.columns:  # Tushare格式
                    date = pd.to_datetime(str(row['trade_date']), format='%Y%m%d')
                    open_price = row['open']
                    high_price = row['high']
                    low_price = row['low']
                    close_price = row['close']
                    volume = row['vol']
                elif '日期' in df.columns:  # AKShare中文格式
                    date = pd.to_datetime(row['日期'])
                    open_price = row['开盘']
                    high_price = row['最高']
                    low_price = row['最低']
                    close_price = row['收盘']
                    volume = row['成交量']
                elif 'date' in df.columns:  # 英文格式
                    date = pd.to_datetime(row['date'])
                    open_price = row['open']
                    high_price = row['high']
                    low_price = row['low']
                    close_price = row['close']
                    volume = row['volume']
                else:
                    # 尝试自动检测列名
                    date_col = None
                    for col in df.columns:
                        if 'date' in col.lower() or '日期' in col:
                            date_col = col
                            break

                    if date_col is None:
                        logger.error(f"无法识别日期列，可用列: {list(df.columns)}")
                        continue

                    date = pd.to_datetime(row[date_col])
                    # 尝试找到价格列
                    open_price = row.get('open', row.get('开盘', 0))
                    high_price = row.get('high', row.get('最高', 0))
                    low_price = row.get('low', row.get('最低', 0))
                    close_price = row.get('close', row.get('收盘', 0))
                    volume = row.get('volume', row.get('vol', row.get('成交量', 0)))

                # 数据验证
                if pd.isna(date) or pd.isna(close_price):
                    continue

                # 确保价格数据有效
                try:
                    open_price = float(open_price) if not pd.isna(open_price) else float(close_price)
                    high_price = float(high_price) if not pd.isna(high_price) else float(close_price)
                    low_price = float(low_price) if not pd.isna(low_price) else float(close_price)
                    close_price = float(close_price)
                    volume = int(volume) if not pd.isna(volume) else 0
                except (ValueError, TypeError):
                    logger.warning(f"价格数据无效，跳过该行")
                    continue

                stock_data = StockData(
                    symbol=symbol,
                    date=date,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume
                )
                stock_data_list.append(stock_data)

            except Exception as e:
                logger.warning(f"转换数据行失败: {e}")
                logger.warning(f"行数据: {dict(row)}")
                continue
        
        return sorted(stock_data_list, key=lambda x: x.date)
    
    def _analyze_zigzag(self, stock_data_list: List[StockData]) -> ZigZagState:
        """进行ZigZag分析"""
        if not stock_data_list:
            return None

        symbol = stock_data_list[0].symbol

        # 转换为zigzag_analysis模块兼容的数据格式
        from zigzag_analysis import StockData as ZigZagStockData
        from decimal import Decimal

        # 计算成交量移动平均
        volumes = [data.volume for data in stock_data_list]
        vol5_list = []
        vol20_list = []

        for i in range(len(volumes)):
            # 5日成交量均线
            start_5 = max(0, i - 4)
            vol5 = sum(volumes[start_5:i+1]) / (i - start_5 + 1)
            vol5_list.append(vol5)

            # 20日成交量均线
            start_20 = max(0, i - 19)
            vol20 = sum(volumes[start_20:i+1]) / (i - start_20 + 1)
            vol20_list.append(vol20)

        # 创建兼容的数据列表
        zigzag_data_list = []
        for i, data in enumerate(stock_data_list):
            zigzag_data = ZigZagStockData(
                symbol=data.symbol,
                date=data.date,
                open=Decimal(str(float(data.open))),
                high=Decimal(str(float(data.high))),
                low=Decimal(str(float(data.low))),
                close=Decimal(str(float(data.close))),
                volume=int(data.volume),
                vol5=Decimal(str(vol5_list[i])),
                vol20=Decimal(str(vol20_list[i]))
            )
            zigzag_data_list.append(zigzag_data)

        # 使用ZigZagCalculator计算ZigZag点
        analyzed_data = self.zigzag_calculator.calculate_zigzag(zigzag_data_list)

        # 创建ZigZagState并填充确认点
        zigzag_state = ZigZagState(symbol=symbol)

        # 从分析结果中提取ZigZag点
        from zigzag import ZigZagPoint
        from decimal import Decimal

        for data in analyzed_data:
            if data.is_zigzag_point:
                # 确保价格是Decimal类型
                price_value = data.high if data.zigzag_point_type == 'HIGH' else data.low
                if isinstance(price_value, Decimal):
                    price = price_value
                else:
                    price = Decimal(str(float(price_value)))

                point = ZigZagPoint(
                    date=data.date,
                    point_type=data.zigzag_point_type,
                    price=price
                )
                zigzag_state.confirmed_points.append(point)

        return zigzag_state
    
    def _analyze_breakthrough_pullback(self, stock_data_list: List[StockData],
                                     zigzag_state: ZigZagState) -> Dict:
        """
        基于ZigZag模型的专业突破回调分析

        突破回调模式定义：
        1. 有明显的峰谷结构 (至少4个ZigZag确认点)
        2. 最近有突破前期重要高点
        3. 突破后有健康回调但未跌破关键支撑
        4. 当前价格处于相对强势位置
        5. 成交量在突破时有所放大
        """
        analysis = {
            'has_breakthrough_pullback': False,
            'breakthrough_strength': 0.0,
            'pullback_depth': 0.0,
            'current_position': 'unknown',
            'peak_valley_analysis': {},
            'recent_patterns': [],
            'zigzag_analysis': {},
            'volume_analysis': {},
            'support_resistance': {}
        }

        if len(zigzag_state.confirmed_points) < 4:
            analysis['reason'] = '确认的ZigZag点数量不足，需要至少4个确认点'
            return analysis

        current_price = float(stock_data_list[-1].close)
        current_date = stock_data_list[-1].date

        # 1. ZigZag峰谷分析
        zigzag_analysis = self._analyze_zigzag_structure(zigzag_state.confirmed_points)
        analysis['zigzag_analysis'] = zigzag_analysis

        # 2. 峰谷统计分析
        peak_valley_stats = self._calculate_peak_valley_stats(zigzag_state.confirmed_points)
        analysis['peak_valley_analysis'] = peak_valley_stats

        # 3. 支撑阻力位分析
        support_resistance = self._identify_support_resistance(zigzag_state.confirmed_points, current_price)
        analysis['support_resistance'] = support_resistance

        # 4. 成交量分析
        volume_analysis = self._analyze_volume_pattern(stock_data_list, zigzag_state.confirmed_points)
        analysis['volume_analysis'] = volume_analysis

        # 5. 突破模式识别
        breakthrough_info = self._identify_breakthrough_pattern(
            zigzag_state.confirmed_points, current_price, current_date, volume_analysis
        )

        # 6. 回调分析
        if breakthrough_info['has_breakthrough']:
            pullback_info = self._analyze_pullback_pattern(
                zigzag_state.confirmed_points, current_price, breakthrough_info
            )
            breakthrough_info.update(pullback_info)

        # 7. 综合评估
        if breakthrough_info['has_breakthrough']:
            analysis['has_breakthrough_pullback'] = True
            analysis['breakthrough_strength'] = breakthrough_info['strength']
            analysis['pullback_depth'] = breakthrough_info.get('pullback_depth', 0.0)
            analysis['current_position'] = breakthrough_info['position']
            analysis['recent_patterns'] = breakthrough_info['patterns']
            analysis['breakthrough_details'] = breakthrough_info

        return analysis

    def _analyze_zigzag_structure(self, confirmed_points) -> Dict:
        """分析ZigZag结构特征"""
        if len(confirmed_points) < 4:
            return {'valid': False, 'reason': '点数不足'}

        # 按时间排序
        points = sorted(confirmed_points, key=lambda x: x.date)

        # 检查交替性 (HIGH-LOW-HIGH-LOW...)
        alternating = True
        for i in range(1, len(points)):
            if points[i].point_type == points[i-1].point_type:
                alternating = False
                break

        # 计算波动幅度
        amplitudes = []
        for i in range(1, len(points)):
            prev_price = float(points[i-1].price)
            curr_price = float(points[i].price)
            amplitude = abs(curr_price - prev_price) / prev_price * 100
            amplitudes.append(amplitude)

        # 趋势分析
        highs = [float(p.price) for p in points if p.point_type == 'HIGH']
        lows = [float(p.price) for p in points if p.point_type == 'LOW']

        trend_direction = 'unknown'
        if len(highs) >= 2 and len(lows) >= 2:
            recent_high_trend = 'up' if highs[-1] > highs[-2] else 'down'
            recent_low_trend = 'up' if lows[-1] > lows[-2] else 'down'

            if recent_high_trend == 'up' and recent_low_trend == 'up':
                trend_direction = 'uptrend'
            elif recent_high_trend == 'down' and recent_low_trend == 'down':
                trend_direction = 'downtrend'
            else:
                trend_direction = 'sideways'

        return {
            'valid': True,
            'total_points': len(points),
            'alternating': alternating,
            'avg_amplitude': sum(amplitudes) / len(amplitudes) if amplitudes else 0,
            'max_amplitude': max(amplitudes) if amplitudes else 0,
            'min_amplitude': min(amplitudes) if amplitudes else 0,
            'trend_direction': trend_direction,
            'recent_highs': highs[-3:] if len(highs) >= 3 else highs,
            'recent_lows': lows[-3:] if len(lows) >= 3 else lows,
            'time_span_days': (points[-1].date - points[0].date).days
        }

    def _identify_support_resistance(self, confirmed_points, current_price) -> Dict:
        """识别关键支撑阻力位"""
        if len(confirmed_points) < 3:
            return {'support_levels': [], 'resistance_levels': []}

        points = sorted(confirmed_points, key=lambda x: x.date)

        # 提取支撑位 (LOW点)
        support_levels = []
        resistance_levels = []

        for point in points:
            price = float(point.price)
            if point.point_type == 'LOW':
                support_levels.append({
                    'price': price,
                    'date': point.date,
                    'distance_from_current': abs(price - current_price) / current_price * 100
                })
            else:  # HIGH
                resistance_levels.append({
                    'price': price,
                    'date': point.date,
                    'distance_from_current': abs(price - current_price) / current_price * 100
                })

        # 按距离当前价格排序
        support_levels.sort(key=lambda x: x['distance_from_current'])
        resistance_levels.sort(key=lambda x: x['distance_from_current'])

        # 找到最近的支撑和阻力
        nearest_support = None
        nearest_resistance = None

        for support in support_levels:
            if support['price'] < current_price:
                nearest_support = support
                break

        for resistance in resistance_levels:
            if resistance['price'] > current_price:
                nearest_resistance = resistance
                break

        return {
            'support_levels': support_levels[:3],  # 最近3个支撑位
            'resistance_levels': resistance_levels[:3],  # 最近3个阻力位
            'nearest_support': nearest_support,
            'nearest_resistance': nearest_resistance
        }

    def _analyze_volume_pattern(self, stock_data_list: List[StockData], confirmed_points) -> Dict:
        """分析成交量模式"""
        if len(stock_data_list) < 20:
            return {'valid': False, 'reason': '数据不足'}

        # 计算平均成交量
        volumes = [data.volume for data in stock_data_list[-30:]]  # 最近30天
        avg_volume = sum(volumes) / len(volumes)

        # 分析ZigZag点附近的成交量
        volume_at_points = []
        for point in confirmed_points[-5:]:  # 最近5个点
            # 找到对应日期的成交量
            for data in stock_data_list:
                if data.date.date() == point.date.date():
                    volume_ratio = data.volume / avg_volume
                    volume_at_points.append({
                        'date': point.date,
                        'type': point.point_type,
                        'volume': data.volume,
                        'volume_ratio': volume_ratio,
                        'is_high_volume': volume_ratio > 1.5
                    })
                    break

        # 最近突破时的成交量
        recent_volume = stock_data_list[-1].volume
        recent_volume_ratio = recent_volume / avg_volume

        return {
            'valid': True,
            'avg_volume_30d': avg_volume,
            'recent_volume': recent_volume,
            'recent_volume_ratio': recent_volume_ratio,
            'volume_at_zigzag_points': volume_at_points,
            'high_volume_breakouts': [v for v in volume_at_points if v['is_high_volume']]
        }

    def _calculate_peak_valley_stats(self, confirmed_points) -> Dict:
        """计算峰谷统计数据"""
        if len(confirmed_points) < 2:
            return {}
        
        peaks = [p for p in confirmed_points if p.point_type == 'HIGH']
        valleys = [p for p in confirmed_points if p.point_type == 'LOW']
        
        stats = {
            'total_peaks': len(peaks),
            'total_valleys': len(valleys),
            'avg_peak_valley_amplitude': 0.0,
            'max_amplitude': 0.0,
            'recent_amplitude_trend': 'stable'
        }
        
        # 计算峰谷幅度
        amplitudes = []
        for i in range(len(confirmed_points) - 1):
            current = confirmed_points[i]
            next_point = confirmed_points[i + 1]
            amplitude = abs(next_point.price - current.price) / current.price * 100
            amplitudes.append(amplitude)
        
        if amplitudes:
            stats['avg_peak_valley_amplitude'] = sum(amplitudes) / len(amplitudes)
            stats['max_amplitude'] = max(amplitudes)
            
            # 分析最近的幅度趋势
            if len(amplitudes) >= 4:
                recent_avg = sum(amplitudes[-2:]) / 2
                earlier_avg = sum(amplitudes[-4:-2]) / 2
                if recent_avg > earlier_avg * 1.2:
                    stats['recent_amplitude_trend'] = 'increasing'
                elif recent_avg < earlier_avg * 0.8:
                    stats['recent_amplitude_trend'] = 'decreasing'
        
        return stats

    def _identify_breakthrough_pattern(self, confirmed_points, current_price, current_date, volume_analysis) -> Dict:
        """基于ZigZag的专业突破模式识别"""
        result = {
            'has_breakthrough': False,
            'strength': 0.0,
            'position': 'unknown',
            'patterns': [],
            'breakthrough_type': 'none',
            'key_levels': {}
        }

        if len(confirmed_points) < 4:
            result['reason'] = 'ZigZag点数不足'
            return result

        # 按时间排序，获取最近的点
        points = sorted(confirmed_points, key=lambda x: x.date)
        recent_points = points[-6:] if len(points) >= 6 else points  # 最近6个点

        # 分离峰和谷
        peaks = [p for p in recent_points if p.point_type == 'HIGH']
        valleys = [p for p in recent_points if p.point_type == 'LOW']

        if len(peaks) < 2:
            result['reason'] = '峰点数不足'
            return result

        # 1. 检查阻力位突破
        breakthrough_info = self._check_resistance_breakthrough(peaks, current_price)

        # 2. 检查趋势突破
        trend_breakthrough = self._check_trend_breakthrough(points, current_price)

        # 3. 成交量确认
        volume_confirmation = self._check_volume_confirmation(volume_analysis)

        # 4. 综合评估突破强度
        total_strength = 0
        patterns = []

        # 阻力位突破评分
        if breakthrough_info['has_breakthrough']:
            total_strength += breakthrough_info['strength']
            patterns.extend(breakthrough_info['patterns'])
            result['breakthrough_type'] = 'resistance_breakthrough'
            result['key_levels']['resistance_level'] = breakthrough_info['resistance_level']

        # 趋势突破评分
        if trend_breakthrough['has_breakthrough']:
            total_strength += trend_breakthrough['strength'] * 0.7  # 趋势突破权重稍低
            patterns.extend(trend_breakthrough['patterns'])
            if result['breakthrough_type'] == 'none':
                result['breakthrough_type'] = 'trend_breakthrough'

        # 成交量确认加分
        if volume_confirmation['confirmed']:
            total_strength += volume_confirmation['bonus']
            patterns.append(f"成交量确认 (+{volume_confirmation['bonus']:.0f}分)")

        # 5. 位置评估
        position = self._evaluate_current_position(points, current_price)

        # 6. 最终判断
        if total_strength >= 50 and len(patterns) >= 1:
            result['has_breakthrough'] = True
            result['strength'] = min(total_strength, 100)  # 最高100分
            result['position'] = position
            result['patterns'] = patterns

        return result

    def _check_resistance_breakthrough(self, peaks, current_price) -> Dict:
        """检查阻力位突破"""
        result = {
            'has_breakthrough': False,
            'strength': 0.0,
            'patterns': [],
            'resistance_level': None
        }

        if len(peaks) < 2:
            return result

        # 按时间排序
        peaks = sorted(peaks, key=lambda x: x.date)

        # 寻找重要阻力位
        for i in range(len(peaks) - 1):
            resistance_peak = peaks[i]
            resistance_price = float(resistance_peak.price)

            # 检查当前价格是否突破了这个阻力位
            if current_price > resistance_price:
                breakthrough_strength = (current_price - resistance_price) / resistance_price * 100

                # 计算突破强度 (简化版)
                strength = min(breakthrough_strength * 10, 50)  # 最高50分

                if strength > result['strength']:
                    result['has_breakthrough'] = True
                    result['strength'] = strength
                    result['resistance_level'] = resistance_price
                    result['patterns'] = [
                        f"突破阻力位 {resistance_price:.2f}",
                        f"突破幅度 {breakthrough_strength:.1f}%"
                    ]

        return result

    def _check_trend_breakthrough(self, points, current_price) -> Dict:
        """检查趋势突破"""
        result = {
            'has_breakthrough': False,
            'strength': 0.0,
            'patterns': []
        }

        if len(points) < 4:
            return result

        # 分析最近的趋势
        highs = [float(p.price) for p in points if p.point_type == 'HIGH']
        lows = [float(p.price) for p in points if p.point_type == 'LOW']

        # 检查上升趋势
        if len(highs) >= 2 and len(lows) >= 2:
            # 检查当前价格是否创新高
            if current_price > max(highs):
                new_high_strength = (current_price - max(highs)) / max(highs) * 100

                result['has_breakthrough'] = True
                result['strength'] = min(new_high_strength * 15, 40)  # 最高40分
                result['patterns'] = [
                    f"创新高突破",
                    f"新高幅度 {new_high_strength:.1f}%"
                ]

        return result

    def _check_volume_confirmation(self, volume_analysis) -> Dict:
        """检查成交量确认"""
        result = {
            'confirmed': False,
            'bonus': 0.0
        }

        if not volume_analysis.get('valid', False):
            return result

        recent_volume_ratio = volume_analysis.get('recent_volume_ratio', 1.0)

        # 成交量放大确认突破
        if recent_volume_ratio > 1.5:
            result['confirmed'] = True
            result['bonus'] = min((recent_volume_ratio - 1) * 10, 20)  # 最高20分

        return result

    def _evaluate_current_position(self, points, current_price) -> str:
        """评估当前位置"""
        if not points:
            return 'unknown'

        highs = [float(p.price) for p in points if p.point_type == 'HIGH']
        lows = [float(p.price) for p in points if p.point_type == 'LOW']

        if not highs or not lows:
            return 'unknown'

        max_high = max(highs)
        min_low = min(lows)

        # 计算当前位置
        if current_price > max_high * 0.95:
            return 'strong'
        elif current_price > (max_high + min_low) / 2:
            return 'moderate'
        else:
            return 'weak'

    def _analyze_pullback_pattern(self, confirmed_points, current_price, breakthrough_info) -> Dict:
        """分析回调模式"""
        result = {
            'pullback_depth': 0.0,
            'pullback_healthy': True
        }

        if not breakthrough_info.get('has_breakthrough', False):
            return result

        # 简化的回调分析
        points = sorted(confirmed_points, key=lambda x: x.date)
        recent_high = max([float(p.price) for p in points if p.point_type == 'HIGH'])

        if current_price < recent_high:
            pullback_depth = (recent_high - current_price) / recent_high * 100
            result['pullback_depth'] = pullback_depth
            result['pullback_healthy'] = pullback_depth < 30  # 回调不超过30%认为健康

        return result

def main():
    """主函数 - 演示突破回调分析"""
    # 示例股票代码（你可以修改这个列表）
    stock_symbols = [
        '000001',  # 平安银行
        '000002',  # 万科A
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '000858',  # 五粮液
        '002415',  # 海康威视
        '600276',  # 恒瑞医药
        '000725',  # 京东方A
        '002594',  # 比亚迪
        '600887'   # 伊利股份
    ]
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=2,
        request_delay=0.8  # 保守的请求间隔
    )
    
    print(f"使用数据源: {data_config.data_source}")
    
    # 创建分析器
    analyzer = BreakthroughAnalyzer(data_config)
    
    # 执行分析
    results = analyzer.analyze_stocks(stock_symbols, years=5)
    
    # 输出结果
    print("\n" + "="*80)
    print("突破回调分析结果")
    print("="*80)
    print(f"分析时间: {results['analysis_date']}")
    print(f"数据范围: {results['time_range']}")
    print(f"总股票数: {results['total_stocks']}")
    print(f"成功分析: {results['successful_analysis']}")
    print(f"发现突破回调模式: {len(results['stocks_with_breakthrough'])} 只")
    
    if results['stocks_with_breakthrough']:
        print("\n🎯 发现突破回调的股票:")
        for i, stock in enumerate(results['stocks_with_breakthrough'][:5], 1):
            print(f"{i}. {stock['symbol']}")
            print(f"   突破强度: {stock['breakthrough_strength']:.1f}%")
            print(f"   回调深度: {stock['pullback_depth']:.1f}%")
            print(f"   当前位置: {stock['current_position']}")
            print()
    else:
        print("\n❌ 未发现明显的突破回调模式")
    
    return results

if __name__ == "__main__":
    main()
