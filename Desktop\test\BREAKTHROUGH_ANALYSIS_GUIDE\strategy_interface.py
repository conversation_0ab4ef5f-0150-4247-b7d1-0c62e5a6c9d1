"""
策略接口模块
为后续策略开发预留扩展点
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
import logging

from data_models import StockData
from zigzag import ZigZagState, ZigZagPoint

logger = logging.getLogger(__name__)

@dataclass
class Signal:
    """交易信号"""
    symbol: str
    date: datetime
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    price: Decimal
    strength: float  # 信号强度 0-100
    confidence: float  # 置信度 0-100
    reason: str  # 信号原因
    metadata: Dict[str, Any] = None  # 额外信息
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class StrategyConfig:
    """策略配置基类"""
    name: str
    version: str = "1.0"
    description: str = ""
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.name = config.name
        
    @abstractmethod
    def analyze(self, 
                stock_data: List[StockData], 
                zigzag_state: ZigZagState,
                context: Dict[str, Any] = None) -> List[Signal]:
        """
        分析股票数据并生成信号
        
        Args:
            stock_data: 股票历史数据
            zigzag_state: ZigZag状态
            context: 额外上下文信息
            
        Returns:
            信号列表
        """
        pass
    
    @abstractmethod
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        pass
    
    def validate_data(self, stock_data: List[StockData]) -> bool:
        """验证数据有效性"""
        if not stock_data:
            return False
        
        # 检查数据完整性
        for data in stock_data[-10:]:  # 检查最近10条数据
            if not all([data.open, data.high, data.low, data.close, data.volume]):
                return False
        
        return True

class ZigZagTrendStrategy(BaseStrategy):
    """基于ZigZag趋势的示例策略"""
    
    def __init__(self, 
                 min_trend_strength: int = 3,
                 min_price_change: float = 0.05):
        config = StrategyConfig(
            name="ZigZag趋势策略",
            description="基于ZigZag拐点识别趋势转换的策略",
            parameters={
                'min_trend_strength': min_trend_strength,
                'min_price_change': min_price_change
            }
        )
        super().__init__(config)
        self.min_trend_strength = min_trend_strength
        self.min_price_change = min_price_change
    
    def analyze(self, 
                stock_data: List[StockData], 
                zigzag_state: ZigZagState,
                context: Dict[str, Any] = None) -> List[Signal]:
        """分析ZigZag趋势并生成信号"""
        
        if not self.validate_data(stock_data):
            return []
        
        signals = []
        current_data = stock_data[-1]  # 最新数据
        
        # 检查是否有足够的拐点
        if len(zigzag_state.confirmed_points) < 2:
            return signals
        
        # 分析最近的趋势
        recent_points = zigzag_state.confirmed_points[-2:]
        
        # 趋势转换信号
        if len(recent_points) >= 2:
            prev_point = recent_points[-2]
            last_point = recent_points[-1]
            
            # 上升趋势信号
            if (prev_point.point_type == 'LOW' and 
                last_point.point_type == 'HIGH' and
                zigzag_state.bars_in_trend >= self.min_trend_strength):
                
                price_change = float((last_point.price - prev_point.price) / prev_point.price)
                
                if price_change >= self.min_price_change:
                    signal = Signal(
                        symbol=current_data.symbol,
                        date=current_data.date,
                        signal_type='BUY',
                        price=current_data.close,
                        strength=min(100, price_change * 100),
                        confidence=80.0,
                        reason=f"ZigZag上升趋势确认，涨幅{price_change:.1%}",
                        metadata={
                            'prev_low': float(prev_point.price),
                            'last_high': float(last_point.price),
                            'trend_bars': zigzag_state.bars_in_trend
                        }
                    )
                    signals.append(signal)
            
            # 下降趋势信号
            elif (prev_point.point_type == 'HIGH' and 
                  last_point.point_type == 'LOW' and
                  zigzag_state.bars_in_trend >= self.min_trend_strength):
                
                price_change = float((prev_point.price - last_point.price) / prev_point.price)
                
                if price_change >= self.min_price_change:
                    signal = Signal(
                        symbol=current_data.symbol,
                        date=current_data.date,
                        signal_type='SELL',
                        price=current_data.close,
                        strength=min(100, price_change * 100),
                        confidence=80.0,
                        reason=f"ZigZag下降趋势确认，跌幅{price_change:.1%}",
                        metadata={
                            'prev_high': float(prev_point.price),
                            'last_low': float(last_point.price),
                            'trend_bars': zigzag_state.bars_in_trend
                        }
                    )
                    signals.append(signal)
        
        return signals
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'trend_following',
            'parameters': self.config.parameters,
            'description': self.config.description,
            'signals_generated': ['BUY', 'SELL'],
            'data_requirements': ['stock_data', 'zigzag_state']
        }

class StrategyManager:
    """策略管理器"""
    
    def __init__(self, db_manager: Optional[Any] = None):
        self.strategies: Dict[str, BaseStrategy] = {}
        self.db_manager = db_manager
        
    def register_strategy(self, strategy: BaseStrategy):
        """注册策略"""
        self.strategies[strategy.name] = strategy
        logger.info(f"注册策略: {strategy.name}")
    
    def run_strategy(self, 
                    strategy_name: str,
                    symbol: str,
                    stock_data: List[StockData],
                    zigzag_state: ZigZagState,
                    save_signals: bool = True) -> List[Signal]:
        """
        运行指定策略
        
        Args:
            strategy_name: 策略名称
            symbol: 股票代码
            stock_data: 股票数据
            zigzag_state: ZigZag状态
            save_signals: 是否保存信号到数据库
            
        Returns:
            生成的信号列表
        """
        if strategy_name not in self.strategies:
            raise ValueError(f"策略 {strategy_name} 未注册")
        
        strategy = self.strategies[strategy_name]
        
        try:
            # 运行策略分析
            signals = strategy.analyze(stock_data, zigzag_state)
            
            # 保存信号到数据库
            if save_signals and self.db_manager and signals:
                for signal in signals:
                    self.db_manager.insert_strategy_signal(
                        symbol=signal.symbol,
                        date=signal.date.strftime('%Y-%m-%d'),
                        strategy_name=strategy_name,
                        signal_type=signal.signal_type,
                        signal_strength=signal.strength,
                        price=float(signal.price),
                        confidence=signal.confidence,
                        metadata=signal.metadata
                    )
            
            logger.info(f"策略 {strategy_name} 为 {symbol} 生成 {len(signals)} 个信号")
            return signals
            
        except Exception as e:
            logger.error(f"运行策略 {strategy_name} 失败: {e}")
            return []
    
    def run_strategies_batch(self, 
                           stock_data_dict: Dict[str, List[StockData]],
                           zigzag_states: Dict[str, ZigZagState],
                           strategy_names: List[str] = None) -> Dict[str, Dict[str, List[Signal]]]:
        """
        批量运行策略
        
        Args:
            stock_data_dict: 股票数据字典
            zigzag_states: ZigZag状态字典
            strategy_names: 要运行的策略名称列表，None表示运行所有策略
            
        Returns:
            {strategy_name: {symbol: [Signal]}} 嵌套字典
        """
        if strategy_names is None:
            strategy_names = list(self.strategies.keys())
        
        logger.info(f"开始批量运行 {len(strategy_names)} 个策略")
        
        results = {}
        
        for strategy_name in strategy_names:
            if strategy_name not in self.strategies:
                logger.warning(f"策略 {strategy_name} 未注册，跳过")
                continue
                
            results[strategy_name] = {}
            
            for symbol in stock_data_dict.keys():
                if symbol in zigzag_states:
                    stock_data = stock_data_dict[symbol]
                    zigzag_state = zigzag_states[symbol]
                    
                    signals = self.run_strategy(
                        strategy_name, symbol, stock_data, zigzag_state
                    )
                    
                    if signals:
                        results[strategy_name][symbol] = signals
        
        logger.info("批量策略运行完成")
        return results
    
    def get_strategy_performance(self, strategy_name: str, 
                               start_date: str = None, 
                               end_date: str = None) -> Dict[str, Any]:
        """获取策略表现统计"""
        if not self.db_manager:
            return {}
        
        try:
            # 查询策略信号
            signals_df = self.db_manager.get_strategy_signals(
                strategy_name=strategy_name,
                start_date=start_date,
                end_date=end_date
            )
            
            if signals_df.empty:
                return {'signal_count': 0}
            
            # 统计信息
            stats = {
                'signal_count': len(signals_df),
                'buy_signals': len(signals_df[signals_df['signal_type'] == 'BUY']),
                'sell_signals': len(signals_df[signals_df['signal_type'] == 'SELL']),
                'avg_strength': signals_df['signal_strength'].mean(),
                'avg_confidence': signals_df['confidence'].mean(),
                'symbols_covered': signals_df['symbol'].nunique(),
                'date_range': {
                    'start': signals_df['date'].min(),
                    'end': signals_df['date'].max()
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取策略表现失败: {e}")
            return {}

# 预定义策略实例
def create_default_strategies() -> List[BaseStrategy]:
    """创建默认策略列表"""
    strategies = [
        ZigZagTrendStrategy(min_trend_strength=3, min_price_change=0.05),
        # 可以在这里添加更多策略
    ]
    return strategies

def setup_strategy_manager(db_manager: Any = None) -> StrategyManager:
    """设置策略管理器"""
    manager = StrategyManager(db_manager)
    
    # 注册默认策略
    default_strategies = create_default_strategies()
    for strategy in default_strategies:
        manager.register_strategy(strategy)
    
    return manager

if __name__ == "__main__":
    # 测试策略接口
    print("测试策略接口...")
    
    # 创建策略管理器
    manager = setup_strategy_manager()
    
    # 显示已注册的策略
    print("已注册的策略:")
    for name, strategy in manager.strategies.items():
        info = strategy.get_strategy_info()
        print(f"  - {name}: {info['description']}")
    
    print("策略接口测试完成")
