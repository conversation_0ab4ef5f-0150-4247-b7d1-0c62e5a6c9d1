"""
股票分析系统主入口
整合股票池筛选、ZigZag分析、数据库存储和策略信号生成
"""

import logging
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, List

from stock_pool import StockPoolSelector, StockPoolConfig
from batch_data_loader import BatchDataLoader, BatchDataConfig
from database_schema import DatabaseManager
from batch_zigzag_processor import IntegratedStockAnalyzer, run_full_analysis
from strategy_interface import setup_strategy_manager
from tushare_config import setup_tushare_token, get_tushare_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='股票分析系统')
    parser.add_argument('--mode', choices=['full', 'update', 'strategy', 'config'],
                       default='full', help='运行模式')
    parser.add_argument('--count', type=int, default=100,
                       help='股票池数量')
    parser.add_argument('--days', type=int, default=365,
                       help='历史数据天数')
    parser.add_argument('--db', default='stock_analysis.duckdb',
                       help='数据库路径')
    parser.add_argument('--symbols', nargs='+',
                       help='指定股票代码列表')
    parser.add_argument('--token',
                       help='Tushare API token')
    parser.add_argument('--data-source', choices=['tushare', 'akshare'],
                       default='tushare', help='数据源选择')
    parser.add_argument('--request-delay', type=float, default=None,
                       help='请求间隔时间(秒)，用于控制API调用频率')
    parser.add_argument('--max-workers', type=int, default=None,
                       help='最大并发线程数')

    args = parser.parse_args()

    try:
        # 配置Tushare token
        if args.mode == 'config':
            print("配置Tushare...")
            success = setup_tushare_token(args.token)
            if success:
                print("Tushare配置成功！")
            else:
                print("Tushare配置失败或跳过")
            return

        # 检查并设置数据源配置
        tushare_config = get_tushare_config()
        if args.data_source == 'tushare' and not tushare_config.is_configured():
            print("Tushare未配置，尝试自动配置...")
            success = setup_tushare_token(args.token)
            if not success:
                print("切换到AKShare数据源...")
                args.data_source = 'akshare'

        # 创建数据配置 - 优化速率限制以避免API限制
        # 使用命令行参数或默认的保守设置
        default_workers = 2 if args.data_source == 'tushare' else 3
        default_delay = 0.5 if args.data_source == 'tushare' else 0.3

        data_config = BatchDataConfig(
            tushare_token=tushare_config.get_token(),
            data_source=args.data_source,
            max_workers=args.max_workers if args.max_workers else default_workers,
            request_delay=args.request_delay if args.request_delay else default_delay
        )

        if args.mode == 'full':
            # 完整分析流程
            print(f"开始完整股票分析流程 (数据源: {args.data_source})...")

            try:
                # 使用更新的run_full_analysis函数
                results = run_full_analysis(
                    target_count=args.count,
                    days_history=args.days,
                    db_path=args.db,
                    data_config=data_config
                )
                print(f"\n分析完成！处理了 {len(results.get('stock_pool', []))} 只股票")

            except Exception as e:
                logger.error(f"完整分析失败: {e}")
                print(f"分析失败: {e}")
                print("\n可能的解决方案:")
                print("1. 检查网络连接")
                print("2. 验证Tushare token是否有效")
                print("3. 尝试使用AKShare: --data-source akshare")
                print("4. 减少股票数量: --count 10")

        elif args.mode == 'update':
            # 更新现有数据
            print("更新现有股票池数据...")
            analyzer = IntegratedStockAnalyzer(
                data_config=data_config,
                db_path=args.db
            )
            analyzer.initialize_system()

            try:
                update_results = analyzer.update_existing_pool(args.symbols)
                print(f"更新完成！更新了 {update_results['updated_count']} 只股票")
            finally:
                analyzer.shutdown_system()

        elif args.mode == 'strategy':
            # 运行策略分析
            print("运行策略分析...")
            run_strategy_analysis(args.db, args.symbols)

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"错误: {e}")
        print("\n如果是Tushare相关错误，请尝试:")
        print("1. python main.py --mode config  # 配置Tushare token")
        print("2. python main.py --data-source akshare  # 使用AKShare数据源")

def run_strategy_analysis(db_path: str, symbols: List[str] = None):
    """运行策略分析"""
    db_manager = DatabaseManager(db_path)
    db_manager.connect()

    try:
        # 设置策略管理器
        strategy_manager = setup_strategy_manager(db_manager)

        # 获取股票数据
        if symbols is None:
            # 从数据库获取所有活跃股票
            df = db_manager.conn.execute(
                "SELECT DISTINCT symbol FROM stock_daily_data ORDER BY symbol"
            ).df()
            symbols = df['symbol'].tolist()[:10]  # 限制为前10只进行演示

        print(f"为 {len(symbols)} 只股票运行策略分析...")

        # 为每只股票运行策略
        for symbol in symbols:
            try:
                # 获取最近30天数据
                end_date = datetime.now().strftime('%Y-%m-%d')
                start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

                stock_data = db_manager.get_stock_data_range(symbol, start_date, end_date)
                zigzag_state = db_manager.get_zigzag_state(symbol)

                if stock_data and zigzag_state:
                    # 运行所有策略
                    for strategy_name in strategy_manager.strategies.keys():
                        signals = strategy_manager.run_strategy(
                            strategy_name, symbol, stock_data, zigzag_state
                        )

                        if signals:
                            print(f"  {symbol} - {strategy_name}: {len(signals)} 个信号")
                            for signal in signals:
                                print(f"    {signal.date.strftime('%Y-%m-%d')} {signal.signal_type} "
                                      f"强度:{signal.strength:.1f} 原因:{signal.reason}")

            except Exception as e:
                logger.warning(f"处理 {symbol} 策略分析失败: {e}")

        # 显示策略表现统计
        print("\n策略表现统计:")
        for strategy_name in strategy_manager.strategies.keys():
            stats = strategy_manager.get_strategy_performance(strategy_name)
            if stats.get('signal_count', 0) > 0:
                print(f"  {strategy_name}:")
                print(f"    信号数量: {stats['signal_count']}")
                print(f"    买入信号: {stats['buy_signals']}")
                print(f"    卖出信号: {stats['sell_signals']}")
                print(f"    平均强度: {stats['avg_strength']:.1f}")
                print(f"    覆盖股票: {stats['symbols_covered']}")

    finally:
        db_manager.disconnect()

if __name__ == "__main__":
    main()
