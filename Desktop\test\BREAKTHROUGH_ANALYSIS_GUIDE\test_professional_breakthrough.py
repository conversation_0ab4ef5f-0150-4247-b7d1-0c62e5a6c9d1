"""
测试专业的ZigZag突破分析功能
"""

from breakthrough_analysis import BreakthroughAnalyzer
from batch_data_loader import BatchDataConfig
from tushare_config import get_tushare_config
from datetime import datetime, timedelta

def test_professional_analysis():
    """测试专业的突破分析功能"""
    print("🎯 测试专业ZigZag突破分析")
    print("="*60)
    
    # 测试股票列表
    test_stocks = [
        '000001',  # 平安银行
        '600036',  # 招商银行
        '002415'   # 海康威视
    ]
    
    print(f"测试股票: {', '.join(test_stocks)}")
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare',
        max_workers=1,
        request_delay=1.5  # 保守设置
    )
    
    print(f"数据源: {data_config.data_source}")
    print("开始专业分析...")
    
    # 创建分析器
    analyzer = BreakthroughAnalyzer(data_config)
    
    # 执行分析
    try:
        results = analyzer.analyze_stocks(test_stocks, years=2)
        
        print("\n" + "="*60)
        print("📊 专业分析结果")
        print("="*60)
        
        print(f"成功分析: {results['successful_analysis']}/{results['total_stocks']}")
        print(f"发现突破: {len(results['stocks_with_breakthrough'])} 只")
        
        # 显示详细结果
        for symbol in test_stocks:
            if symbol in results['detailed_results']:
                detail = results['detailed_results'][symbol]
                print(f"\n📈 {symbol} 详细分析:")
                
                # 基本信息
                has_breakthrough = detail.get('has_breakthrough_pullback', False)
                strength = detail.get('breakthrough_strength', 0)
                position = detail.get('current_position', 'unknown')
                
                print(f"   突破状态: {'✅ 有突破' if has_breakthrough else '❌ 无突破'}")
                if has_breakthrough:
                    print(f"   突破强度: {strength:.1f}/100")
                    print(f"   当前位置: {position}")
                
                # ZigZag分析
                if 'zigzag_analysis' in detail:
                    zigzag = detail['zigzag_analysis']
                    if zigzag.get('valid', False):
                        print(f"   ZigZag点数: {zigzag.get('total_points', 0)}")
                        print(f"   平均波幅: {zigzag.get('avg_amplitude', 0):.1f}%")
                        print(f"   趋势方向: {zigzag.get('trend_direction', 'unknown')}")
                
                # 峰谷分析
                if 'peak_valley_analysis' in detail:
                    pv = detail['peak_valley_analysis']
                    print(f"   峰点数量: {pv.get('total_peaks', 0)}")
                    print(f"   谷点数量: {pv.get('total_valleys', 0)}")
                    if pv.get('max_amplitude', 0) > 0:
                        print(f"   最大波幅: {pv.get('max_amplitude', 0):.1f}%")
                
                # 支撑阻力
                if 'support_resistance' in detail:
                    sr = detail['support_resistance']
                    nearest_resistance = sr.get('nearest_resistance')
                    nearest_support = sr.get('nearest_support')
                    
                    if nearest_resistance:
                        print(f"   最近阻力: {nearest_resistance['price']:.2f}")
                    if nearest_support:
                        print(f"   最近支撑: {nearest_support['price']:.2f}")
                
                # 成交量分析
                if 'volume_analysis' in detail:
                    vol = detail['volume_analysis']
                    if vol.get('valid', False):
                        ratio = vol.get('recent_volume_ratio', 1.0)
                        print(f"   成交量比率: {ratio:.1f}x")
                
                # 突破详情
                if 'breakthrough_details' in detail:
                    bd = detail['breakthrough_details']
                    if bd.get('has_breakthrough', False):
                        print(f"   突破类型: {bd.get('breakthrough_type', 'unknown')}")
                        patterns = bd.get('patterns', [])
                        if patterns:
                            print(f"   突破特征:")
                            for pattern in patterns[:3]:  # 显示前3个特征
                                print(f"     - {pattern}")
        
        # 总结
        if results['stocks_with_breakthrough']:
            print(f"\n🎯 突破股票总结:")
            for stock in results['stocks_with_breakthrough']:
                symbol = stock['symbol']
                strength = stock['breakthrough_strength']
                position = stock['current_position']
                
                if strength >= 70:
                    rating = "⭐⭐⭐⭐⭐ 强烈推荐"
                elif strength >= 60:
                    rating = "⭐⭐⭐⭐ 推荐"
                elif strength >= 50:
                    rating = "⭐⭐⭐ 关注"
                else:
                    rating = "⭐⭐ 观望"
                
                print(f"   {symbol}: {rating} (强度:{strength:.0f}, 位置:{position})")
        else:
            print(f"\n📋 本次分析未发现明显的突破回调模式")
            print("   建议:")
            print("   - 继续观察市场变化")
            print("   - 扩大分析股票范围")
            print("   - 调整分析时间周期")
        
        return results
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_professional_analysis()
