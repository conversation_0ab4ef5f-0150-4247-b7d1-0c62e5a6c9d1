"""
专业的ZigZag突破回调分析
避免类型转换问题，使用纯Python实现
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import pandas as pd
import json

from batch_data_loader import BatchDataLoader, BatchDataConfig, StockData
# from data_models import StockData
from tushare_config import get_tushare_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZigZagPoint:
    """ZigZag点数据结构"""
    def __init__(self, date: datetime, point_type: str, price: float):
        self.date = date
        self.point_type = point_type  # 'HIGH' or 'LOW'
        self.price = price

class ProfessionalBreakthroughAnalyzer:
    """专业突破回调分析器"""
    
    def __init__(self, data_config: BatchDataConfig):
        self.data_config = data_config
        self.data_loader = BatchDataLoader(data_config)
    
    def analyze_stocks(self, stock_symbols: List[str], years: int = 2) -> Dict:
        """分析股票列表的突破回调模式"""
        # 计算日期范围
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=years * 365)).strftime('%Y%m%d')
        
        print(f"开始专业分析 {len(stock_symbols)} 只股票的 {years} 年数据...")
        print(f"时间范围: {start_date} 到 {end_date}")
        
        # 批量获取数据
        stock_data_dict = self.data_loader.get_batch_stock_data(
            stock_symbols, start_date, end_date
        )
        
        results = {
            'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'time_range': f"{start_date} 到 {end_date}",
            'total_stocks': len(stock_symbols),
            'successful_analysis': 0,
            'stocks_with_breakthrough': [],
            'detailed_results': {}
        }
        
        # 分析每只股票
        for symbol in stock_symbols:
            try:
                df = stock_data_dict.get(symbol)
                if df is None or df.empty:
                    logger.warning(f"股票 {symbol} 数据为空，跳过分析")
                    continue
                
                # 转换为StockData格式
                stock_data_list = self._convert_to_stock_data(df, symbol)
                
                # 进行ZigZag分析
                zigzag_points = self._calculate_zigzag_points(stock_data_list)
                
                # 专业突破分析
                breakthrough_analysis = self._professional_breakthrough_analysis(
                    stock_data_list, zigzag_points
                )
                
                results['detailed_results'][symbol] = breakthrough_analysis
                results['successful_analysis'] += 1
                
                # 如果发现突破回调模式，加入结果列表
                if breakthrough_analysis['has_breakthrough_pullback']:
                    results['stocks_with_breakthrough'].append({
                        'symbol': symbol,
                        'breakthrough_strength': breakthrough_analysis['breakthrough_strength'],
                        'pullback_depth': breakthrough_analysis['pullback_depth'],
                        'current_position': breakthrough_analysis['current_position']
                    })
                
                print(f"✅ {symbol} 专业分析完成")
                
            except Exception as e:
                logger.error(f"分析股票 {symbol} 失败: {e}")
                continue
        
        # 按突破强度排序
        results['stocks_with_breakthrough'].sort(
            key=lambda x: x['breakthrough_strength'], reverse=True
        )
        
        return results
    
    def _convert_to_stock_data(self, df: pd.DataFrame, symbol: str) -> List[StockData]:
        """将DataFrame转换为StockData列表"""
        stock_data_list = []
        
        for _, row in df.iterrows():
            try:
                if 'date' in df.columns:
                    date = pd.to_datetime(row['date'])
                    open_price = float(row['open'])
                    high_price = float(row['high'])
                    low_price = float(row['low'])
                    close_price = float(row['close'])
                    volume = int(row['volume'])
                else:
                    continue
                
                stock_data = StockData(
                    symbol=symbol,
                    date=date,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume
                )
                stock_data_list.append(stock_data)
                
            except Exception as e:
                logger.warning(f"转换数据行失败: {e}")
                continue
        
        return sorted(stock_data_list, key=lambda x: x.date)
    
    def _calculate_zigzag_points(self, stock_data_list: List[StockData], 
                                threshold: float = 0.05) -> List[ZigZagPoint]:
        """计算ZigZag点 - 简化版本"""
        if len(stock_data_list) < 10:
            return []
        
        zigzag_points = []
        
        # 寻找局部极值点
        for i in range(5, len(stock_data_list) - 5):
            current = stock_data_list[i]
            
            # 检查是否是局部高点
            is_high = True
            is_low = True
            
            for j in range(i - 5, i + 6):
                if j != i and j >= 0 and j < len(stock_data_list):
                    if stock_data_list[j].high >= current.high:
                        is_high = False
                    if stock_data_list[j].low <= current.low:
                        is_low = False
            
            # 添加显著的极值点
            if is_high:
                # 检查与前一个高点的距离
                if not zigzag_points or zigzag_points[-1].point_type != 'HIGH':
                    if not zigzag_points or abs(current.high - zigzag_points[-1].price) / zigzag_points[-1].price > threshold:
                        zigzag_points.append(ZigZagPoint(current.date, 'HIGH', current.high))
            
            elif is_low:
                # 检查与前一个低点的距离
                if not zigzag_points or zigzag_points[-1].point_type != 'LOW':
                    if not zigzag_points or abs(current.low - zigzag_points[-1].price) / zigzag_points[-1].price > threshold:
                        zigzag_points.append(ZigZagPoint(current.date, 'LOW', current.low))
        
        return zigzag_points
    
    def _professional_breakthrough_analysis(self, stock_data_list: List[StockData], 
                                          zigzag_points: List[ZigZagPoint]) -> Dict:
        """专业的突破回调分析"""
        analysis = {
            'has_breakthrough_pullback': False,
            'breakthrough_strength': 0.0,
            'pullback_depth': 0.0,
            'current_position': 'unknown',
            'zigzag_analysis': {},
            'peak_valley_analysis': {},
            'support_resistance': {},
            'volume_analysis': {},
            'breakthrough_details': {}
        }
        
        if len(zigzag_points) < 4:
            analysis['reason'] = 'ZigZag点数量不足'
            return analysis
        
        current_price = stock_data_list[-1].close
        current_date = stock_data_list[-1].date
        
        # 1. ZigZag结构分析
        zigzag_analysis = self._analyze_zigzag_structure(zigzag_points)
        analysis['zigzag_analysis'] = zigzag_analysis
        
        # 2. 峰谷统计
        peak_valley_stats = self._calculate_peak_valley_stats(zigzag_points)
        analysis['peak_valley_analysis'] = peak_valley_stats
        
        # 3. 支撑阻力分析
        support_resistance = self._identify_support_resistance(zigzag_points, current_price)
        analysis['support_resistance'] = support_resistance
        
        # 4. 成交量分析
        volume_analysis = self._analyze_volume_pattern(stock_data_list, zigzag_points)
        analysis['volume_analysis'] = volume_analysis
        
        # 5. 突破模式识别
        breakthrough_info = self._identify_breakthrough_pattern(
            zigzag_points, current_price, volume_analysis
        )
        analysis['breakthrough_details'] = breakthrough_info
        
        # 6. 回调分析
        if breakthrough_info['has_breakthrough']:
            pullback_info = self._analyze_pullback_pattern(zigzag_points, current_price)
            breakthrough_info.update(pullback_info)
        
        # 7. 综合评估
        if breakthrough_info['has_breakthrough']:
            analysis['has_breakthrough_pullback'] = True
            analysis['breakthrough_strength'] = breakthrough_info['strength']
            analysis['pullback_depth'] = breakthrough_info.get('pullback_depth', 0.0)
            analysis['current_position'] = breakthrough_info['position']
        
        return analysis
    
    def _analyze_zigzag_structure(self, zigzag_points: List[ZigZagPoint]) -> Dict:
        """分析ZigZag结构"""
        if len(zigzag_points) < 2:
            return {'valid': False}
        
        # 检查交替性
        alternating = True
        for i in range(1, len(zigzag_points)):
            if zigzag_points[i].point_type == zigzag_points[i-1].point_type:
                alternating = False
                break
        
        # 计算波动幅度
        amplitudes = []
        for i in range(1, len(zigzag_points)):
            prev_price = zigzag_points[i-1].price
            curr_price = zigzag_points[i].price
            amplitude = abs(curr_price - prev_price) / prev_price * 100
            amplitudes.append(amplitude)
        
        return {
            'valid': True,
            'total_points': len(zigzag_points),
            'alternating': alternating,
            'avg_amplitude': sum(amplitudes) / len(amplitudes) if amplitudes else 0,
            'max_amplitude': max(amplitudes) if amplitudes else 0
        }
    
    def _calculate_peak_valley_stats(self, zigzag_points: List[ZigZagPoint]) -> Dict:
        """计算峰谷统计"""
        peaks = [p for p in zigzag_points if p.point_type == 'HIGH']
        valleys = [p for p in zigzag_points if p.point_type == 'LOW']
        
        return {
            'total_peaks': len(peaks),
            'total_valleys': len(valleys),
            'recent_peaks': [p.price for p in peaks[-3:]] if len(peaks) >= 3 else [p.price for p in peaks],
            'recent_valleys': [p.price for p in valleys[-3:]] if len(valleys) >= 3 else [p.price for p in valleys]
        }
    
    def _identify_support_resistance(self, zigzag_points: List[ZigZagPoint], current_price: float) -> Dict:
        """识别支撑阻力位"""
        support_levels = [p.price for p in zigzag_points if p.point_type == 'LOW' and p.price < current_price]
        resistance_levels = [p.price for p in zigzag_points if p.point_type == 'HIGH' and p.price > current_price]
        
        return {
            'nearest_support': max(support_levels) if support_levels else None,
            'nearest_resistance': min(resistance_levels) if resistance_levels else None,
            'support_count': len(support_levels),
            'resistance_count': len(resistance_levels)
        }
    
    def _analyze_volume_pattern(self, stock_data_list: List[StockData], 
                               zigzag_points: List[ZigZagPoint]) -> Dict:
        """分析成交量模式"""
        if len(stock_data_list) < 20:
            return {'valid': False}
        
        volumes = [data.volume for data in stock_data_list[-30:]]
        avg_volume = sum(volumes) / len(volumes)
        recent_volume = stock_data_list[-1].volume
        
        return {
            'valid': True,
            'avg_volume_30d': avg_volume,
            'recent_volume': recent_volume,
            'volume_ratio': recent_volume / avg_volume
        }
    
    def _identify_breakthrough_pattern(self, zigzag_points: List[ZigZagPoint], 
                                     current_price: float, volume_analysis: Dict) -> Dict:
        """识别突破模式"""
        result = {
            'has_breakthrough': False,
            'strength': 0.0,
            'position': 'unknown',
            'patterns': []
        }
        
        if len(zigzag_points) < 4:
            return result
        
        peaks = [p for p in zigzag_points if p.point_type == 'HIGH']
        if len(peaks) < 2:
            return result
        
        # 检查是否突破重要阻力位
        for peak in peaks[:-1]:  # 排除最后一个峰
            if current_price > peak.price:
                breakthrough_strength = (current_price - peak.price) / peak.price * 100
                
                # 计算突破强度
                strength = min(breakthrough_strength * 10, 60)  # 基础分数
                
                # 成交量确认
                if volume_analysis.get('valid', False):
                    volume_ratio = volume_analysis.get('volume_ratio', 1.0)
                    if volume_ratio > 1.5:
                        strength += 20  # 成交量确认加分
                
                # 位置评估
                max_high = max([p.price for p in peaks])
                if current_price > max_high * 0.95:
                    position = 'strong'
                elif current_price > max_high * 0.85:
                    position = 'moderate'
                else:
                    position = 'weak'
                
                if strength > result['strength']:
                    result['has_breakthrough'] = True
                    result['strength'] = strength
                    result['position'] = position
                    result['patterns'] = [
                        f"突破阻力位 {peak.price:.2f}",
                        f"突破幅度 {breakthrough_strength:.1f}%"
                    ]
                    
                    if volume_analysis.get('volume_ratio', 1.0) > 1.5:
                        result['patterns'].append("成交量放大确认")
        
        return result
    
    def _analyze_pullback_pattern(self, zigzag_points: List[ZigZagPoint], 
                                 current_price: float) -> Dict:
        """分析回调模式"""
        peaks = [p for p in zigzag_points if p.point_type == 'HIGH']
        if not peaks:
            return {'pullback_depth': 0.0}
        
        recent_high = max([p.price for p in peaks])
        
        if current_price < recent_high:
            pullback_depth = (recent_high - current_price) / recent_high * 100
        else:
            pullback_depth = 0.0
        
        return {
            'pullback_depth': pullback_depth,
            'pullback_healthy': pullback_depth < 25  # 回调不超过25%认为健康
        }

    def generate_professional_report(self, results: Dict, output_file: str = None) -> str:
        """生成专业的ZigZag突破分析报告"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"专业ZigZag突破分析报告_{timestamp}.md"

        report_lines = []

        # 报告标题
        report_lines.extend([
            "# 专业ZigZag突破回调分析报告",
            "",
            f"**生成时间**: {results['analysis_date']}",
            f"**分析期间**: {results['time_range']}",
            f"**分析方法**: 基于ZigZag模型的专业突破回调识别",
            f"**分析股票数量**: {results['total_stocks']}",
            f"**成功分析**: {results['successful_analysis']}",
            f"**发现突破模式**: {len(results['stocks_with_breakthrough'])} 只",
            "",
            "---",
            ""
        ])

        # 执行摘要
        breakthrough_count = len(results['stocks_with_breakthrough'])
        if breakthrough_count > 0:
            avg_strength = sum(s['breakthrough_strength'] for s in results['stocks_with_breakthrough']) / breakthrough_count
            strong_count = sum(1 for s in results['stocks_with_breakthrough'] if s['breakthrough_strength'] >= 70)

            summary = f"""
## 📊 执行摘要

**核心发现**: 基于ZigZag模型分析，发现 **{breakthrough_count} 只股票** 呈现突破回调模式。

**技术特征**:
- 突破股票占比: {breakthrough_count/results['successful_analysis']*100:.1f}%
- 平均突破强度: {avg_strength:.1f} 分
- 强势突破 (≥70分): {strong_count} 只

**投资价值**:
- ZigZag确认的技术突破具有较高可信度
- 建议重点关注强势突破标的

---
"""
        else:
            summary = """
## 📊 执行摘要

**核心发现**: 本次ZigZag分析未发现明显的突破回调模式。

**市场状态**: 分析的股票可能处于震荡整理阶段

---
"""

        report_lines.append(summary)

        # 突破股票详细分析
        if results['stocks_with_breakthrough']:
            report_lines.append("## 🎯 突破股票专业分析\n")

            for i, stock in enumerate(results['stocks_with_breakthrough'], 1):
                symbol = stock['symbol']
                strength = stock['breakthrough_strength']
                pullback = stock['pullback_depth']
                position = stock['current_position']

                # 评级
                if strength >= 80:
                    rating = "⭐⭐⭐⭐⭐ 强烈推荐"
                elif strength >= 70:
                    rating = "⭐⭐⭐⭐ 推荐"
                elif strength >= 60:
                    rating = "⭐⭐⭐ 关注"
                else:
                    rating = "⭐⭐ 观望"

                position_desc = {
                    'strong': '强势位置 - 接近突破高点',
                    'moderate': '中等位置 - 有上涨空间',
                    'weak': '弱势位置 - 需要确认'
                }.get(position, '未知位置')

                stock_analysis = f"""
### {i}. {symbol} {rating}

**基本信息**:
- 突破强度: {strength:.1f}/100
- 回调深度: {pullback:.1f}%
- 当前位置: {position_desc}

**ZigZag分析**:
"""

                # 获取详细数据
                if symbol in results['detailed_results']:
                    detail = results['detailed_results'][symbol]

                    # ZigZag结构
                    if 'zigzag_analysis' in detail and detail['zigzag_analysis'].get('valid', False):
                        zz = detail['zigzag_analysis']
                        stock_analysis += f"""- ZigZag点数: {zz.get('total_points', 0)}
- 平均波幅: {zz.get('avg_amplitude', 0):.1f}%
- 最大波幅: {zz.get('max_amplitude', 0):.1f}%
"""

                    # 峰谷统计
                    if 'peak_valley_analysis' in detail:
                        pv = detail['peak_valley_analysis']
                        stock_analysis += f"""- 峰点数量: {pv.get('total_peaks', 0)}
- 谷点数量: {pv.get('total_valleys', 0)}
"""

                    # 支撑阻力
                    if 'support_resistance' in detail:
                        sr = detail['support_resistance']
                        if sr.get('nearest_resistance'):
                            stock_analysis += f"- 最近阻力: {sr['nearest_resistance']:.2f}\n"
                        if sr.get('nearest_support'):
                            stock_analysis += f"- 最近支撑: {sr['nearest_support']:.2f}\n"

                    # 突破详情
                    if 'breakthrough_details' in detail:
                        bd = detail['breakthrough_details']
                        patterns = bd.get('patterns', [])
                        if patterns:
                            stock_analysis += f"""
**突破特征**:
"""
                            for pattern in patterns:
                                stock_analysis += f"- {pattern}\n"

                # 投资建议
                if strength >= 70:
                    suggestion = "建议重点关注，技术形态强势"
                elif strength >= 60:
                    suggestion = "建议持续跟踪，等待进一步确认"
                else:
                    suggestion = "建议观望，风险相对较高"

                stock_analysis += f"""
**投资建议**: {suggestion}

---
"""

                report_lines.append(stock_analysis)

        # 技术方法说明
        methodology = """
## 🔬 ZigZag分析方法论

### 核心原理
ZigZag指标通过连接股价的重要高点和低点，过滤市场噪音，识别主要趋势转折点。

### 分析流程
1. **极值点识别**: 寻找局部高点和低点
2. **趋势确认**: 验证价格突破的有效性
3. **回调分析**: 评估突破后的回调健康度
4. **成交量确认**: 验证突破的可靠性

### 评分体系
- **突破强度**: 基于突破幅度和重要性
- **位置评估**: 当前价格相对于关键位置
- **成交量确认**: 突破时的成交量放大情况

### 优势特点
- 过滤短期波动，专注主要趋势
- 客观识别关键支撑阻力位
- 提供清晰的买卖信号

### 局限性
- 滞后性：确认信号需要时间
- 假突破：需要结合其他指标验证
- 市场环境：极端行情下可能失效

---

## ⚠️ 风险提示

**重要声明**: 本报告基于技术分析，不构成投资建议。

**投资风险**:
- 技术分析存在滞后性和不确定性
- 市场突发事件可能导致技术失效
- 建议结合基本面分析综合判断

**操作建议**:
- 分散投资，控制单一标的风险
- 设置止损位，严格风险管理
- 持续跟踪，动态调整策略
"""

        report_lines.append(methodology)

        # 写入文件
        report_content = "\n".join(report_lines)

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📄 专业分析报告已生成: {output_file}")
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")

        return report_content

def main():
    """主函数"""
    # 测试股票代码
    stock_symbols = [
        '000001',  # 平安银行
        '600036',  # 招商银行
        '600519',  # 贵州茅台
        '002415',  # 海康威视
        '600276'   # 恒瑞医药
    ]
    
    # 配置数据源
    tushare_config = get_tushare_config()
    data_config = BatchDataConfig(
        tushare_token=tushare_config.get_token(),
        data_source='tushare' if tushare_config.is_configured() else 'akshare',
        max_workers=1,
        request_delay=1.5
    )
    
    print(f"使用数据源: {data_config.data_source}")
    
    # 创建分析器
    analyzer = ProfessionalBreakthroughAnalyzer(data_config)
    
    # 执行分析
    results = analyzer.analyze_stocks(stock_symbols, years=2)
    
    # 输出结果
    print("\n" + "="*80)
    print("专业ZigZag突破回调分析结果")
    print("="*80)
    print(f"分析时间: {results['analysis_date']}")
    print(f"成功分析: {results['successful_analysis']}/{results['total_stocks']}")
    print(f"发现突破模式: {len(results['stocks_with_breakthrough'])} 只")
    
    if results['stocks_with_breakthrough']:
        print("\n🎯 发现突破回调的股票:")
        for i, stock in enumerate(results['stocks_with_breakthrough'], 1):
            strength = stock['breakthrough_strength']
            if strength >= 70:
                rating = "⭐⭐⭐⭐⭐ 强烈推荐"
            elif strength >= 60:
                rating = "⭐⭐⭐⭐ 推荐"
            elif strength >= 50:
                rating = "⭐⭐⭐ 关注"
            else:
                rating = "⭐⭐ 观望"
            
            print(f"{i}. {stock['symbol']} {rating}")
            print(f"   突破强度: {strength:.1f}/100")
            print(f"   回调深度: {stock['pullback_depth']:.1f}%")
            print(f"   当前位置: {stock['current_position']}")
            print()
    else:
        print("\n❌ 未发现明显的突破回调模式")

    # 生成专业报告
    print("\n" + "="*80)
    print("正在生成专业ZigZag分析报告...")
    print("="*80)

    try:
        report_content = analyzer.generate_professional_report(results)
        print("✅ 专业报告生成成功！")

        # 同时生成JSON格式的数据
        json_file = f"专业分析数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        print(f"📊 分析数据已保存: {json_file}")

    except Exception as e:
        print(f"❌ 报告生成失败: {e}")

    return results

if __name__ == "__main__":
    main()
